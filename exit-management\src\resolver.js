const axios = require('axios');
const Common = require('../common');
const Helper = require('./helper');
const commonValidation = require('../common/commonValidation');
const { updateStatusAndAssetMapping, updateWorkflowResignationInstanceData,employeeSettlementInitiated }=require('../common/CommonFunctions')
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');

const validateResignationDate = (resignationDate, appliedDate, relievingReasonComment='') => {
  // resignationDate validation
  if (!resignationDate || resignationDate.length === 0) {
    return [
      Common.getError('ERE0108')
    ];
  }else{
    // check resignationDate is valid or not. resignationDate format is YYYY/MM/DD so split the date and validat it
    var tmpResignationDate = resignationDate.split('/');
    if (tmpResignationDate[0] <= 0 || tmpResignationDate[1] <= 0 || tmpResignationDate[2] <= 0 || new Date(resignationDate) === 'Invalid Date') {
      return [
        Common.getError('ERE0111')
      ];
    }
  }
  // appliedDate validation
  if (!appliedDate || appliedDate.length === 0) {
    return [
      Common.getError('ERE0109')
    ];
  }else{
    // check appliedDate is valid or not. appliedDate format is YYYY/MM/DD so split the date and validat it
    var tmpAppliedDate = appliedDate.split('/');
    if (tmpAppliedDate[0] <= 0 || tmpAppliedDate[1] <= 0 || tmpAppliedDate[2] <= 0 || new Date(appliedDate) === 'Invalid Date') {
      return [
        Common.getError('ERE0111')
      ];
    }
  }
  // Check resignation date less than applied date
  if (new Date(resignationDate).getTime() < new Date(appliedDate).getTime()) {
    return [
      Common.getError('ERE0110')
    ];
  }

  /** If the relieving reason comment exist */
  if(relievingReasonComment){
    return validateRelievingReasonComment(relievingReasonComment);
  }
  return [];
};

//Validate relieving reason comment
const validateRelievingReasonComment = (relievingReasonComment='') => {
  //Validate comment
  if(relievingReasonComment){
    let validateComment;
    validateComment = commonValidation.commentValidation(relievingReasonComment); // comment validation
    if (!validateComment) {
      let getCommentErrorCode = Common.getError('ERE0115');
      let commentValidationResponseMessage = {
        code: getCommentErrorCode.code,
        message: getCommentErrorCode.message2
      };
      return [ commentValidationResponseMessage ];
    }else{
      validateComment = commonValidation.commentLengthValidation(relievingReasonComment); // comment length validation
      if (!validateComment) {
        let getCommentErrorCode = Common.getError('ERE0115');
        let commentValidationResponseMessage = {
          code: getCommentErrorCode.code,
          message: getCommentErrorCode.message3
        };
        return [ commentValidationResponseMessage ];
      }
    }
  }
  return [];
};

//Validate update resignation reason inputs
const validateUpdateResignationReasonInputs = (args) => {
  let {resignationId,relievingReasonComment} = args;

  //Validate resignation id
  if(!resignationId){
    return [Common.getError('ERE0015')];
  }

  //Validate relieving reason
  if(relievingReasonComment){
    let validateComment = validateRelievingReasonComment(relievingReasonComment);
    if(validateComment.length > 0){
      return validateComment;
    }
  }

  return [];
};

//Validate withdrawn/cancellation resignation inputs
const validateWithdrawnCancellationInputs = (args) => {
  let {resignationId,approvalStatus,comment} = args;

  //Validate resignation id
  if(!resignationId){
    return [Common.getError('ERE0015')];
  }

  let invalidApprovalStatus = ['Withdrawn','Canceled'];
  //Validate approval status
  if(!approvalStatus || !invalidApprovalStatus.includes(approvalStatus)){
    return[Common.getError('ERE0121')];
  }

  //Validate withdrawn or cancellation reason
  if(comment){
    let validateComment = validateRelievingReasonComment(comment);
    if(validateComment.length > 0){
      return validateComment;
    }
  }else{
    let getCommentErrorCode = Common.getError('ERE0115');
    let commentValidationResponseMessage = {
      code: getCommentErrorCode.code,
      message: getCommentErrorCode.message1
    };
    return [ commentValidationResponseMessage ];
  }

  return [];
};

const resolvers = {
  Query: {
    /**
     * For get all employee list for search list
     */
    getAllEmployees: async (parent, args, root) => {
      const { envelope, filter } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        let isManager = false;
        let isAdmin = false;
        let isServiceProviderAdmin = false;
        
        let checkAccessRights = await Common.checkEmployeeAccessRights(
            organizationDbConnection,
            envelope.loggedInUserId,
            null,
            '',
            'ui',
            false,
            envelope.formId ? envelope.formId: Common.constant.formIdResignation
        );
        
        if(Object.keys(checkAccessRights).length > 0){
          //If error occurred while retrieving the access
          if(checkAccessRights.error){
            console.log('Error from the checkEmployeeAccessRights() in the getAllEmployees() function.',checkAccessRights);
            response.error = checkAccessRights.error
                ? checkAccessRights.error
                : Common.getError(null, '_DB0100');
            // destroy database connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return response;
          }else{
            checkAccessRights= JSON.parse(checkAccessRights);
            var subEmployeeIds = [];
            if(checkAccessRights.Employee_Role !== 'admin'){
              /** If the login emp is not an admin then check whether they are service provider admin or not. 
                 * if the employee is not a service provider admin then check whether the emp is manager or not */
                let serviceProviderFormAccessRights = await Common.checkEmployeeAccessRights(
                  organizationDbConnection,
                  envelope.loggedInUserId,
                  Common.constant.formServiceProviderAdmin,
                  '',
                  'ui');

                  if (Object.keys(serviceProviderFormAccessRights).length > 0) {
                    if(serviceProviderFormAccessRights.error){
                      console.log('Error when getting service provider accessrights in the getAllEmployees() function.',accessRights);
                      response.error = serviceProviderFormAccessRights.error
                          ? serviceProviderFormAccessRights.error
                          : Common.getError(null, '_DB0100');
                      // destroy database connection
                      organizationDbConnection ? organizationDbConnection.destroy() : null;
                      return response;
                    }else{
                      serviceProviderFormAccessRights = JSON.parse(serviceProviderFormAccessRights);
                      if(serviceProviderFormAccessRights.Role_Update){
                        isServiceProviderAdmin = true;
                      } else if(checkAccessRights.Is_Manager){
                          isManager = true;
                      }
                    }
                  }else{
                    if(checkAccessRights.Is_Manager){
                      isManager = true;
                    }
                  }
            }else{
              isAdmin = true;
            }

            if (isServiceProviderAdmin){
              // fetch empployees associated with the service provider
              const serviceProviderId = await Helper.getServiceProviderId(
                organizationDbConnection,
                envelope.loggedInUserId
              );
              
              subEmployeeIds = await Helper.getAllServiceProviderEmployees(
                organizationDbConnection,
                serviceProviderId
              );
              
            }else if (isManager) {
              // for fetch manager level reporting employees list
              subEmployeeIds = await Helper.getAllSubEmployee(
                organizationDbConnection,
                envelope.loggedInUserId
              )
            }

            const alreadyAppliedOrApprovedEmployeeIds = await (() => {
              return new Promise(resolve => {
                const query = organizationDbConnection.select('Employee_Id');
                query.from('emp_resignation');
                query.where(db => {
                  db.whereIn('Approval_Status', [
                    Common.constant.statusApplied,
                    Common.constant.statusApproved,
                    Common.constant.statusIncomplete
                  ]);
                  if (isManager) {
                    db.whereIn('Employee_Id', subEmployeeIds)
                  }
                });
                query.then(result => {
                  resolve(
                      result.map(row => {
                        return row.Employee_Id
                      })
                  )
                })
              })
            })();
    
            const query = organizationDbConnection.select(
                'emp_job.Employee_Id as id',
                'emp_job.User_Defined_EmpId as idPrefix',
                organizationDbConnection.raw(
                    'CONCAT(emp_personal_info.Emp_First_Name, \' \', emp_personal_info.Emp_Last_Name) as "name"'
                ),
                'emp_personal_info.Photo_Path as photoPath',
                'emp_job.Designation_Id as designationId',
                'designation.Designation_Name as designationName',
                'emp_job.Department_Id as departmentId',
                'department.Department_Name as departmentName',
                'emp_personal_info.Is_Manager as isManager',
                'emp_personal_info.Gender as gender', 'emp_personal_info.Gender_Id as genderId',
                'emp_job.Emp_Status as status',
                 'location.Location_Id as locationId','location.Location_Name as locationName',
                'employee_type.Employee_Type as employeeType','employee_type.EmpType_Id as empTypeId'
            );
            query.from('emp_job');
            query.leftJoin(
                'emp_personal_info',
                'emp_job.Employee_Id',
                'emp_personal_info.Employee_Id'
            );
            query.leftJoin(
                'designation',
                'emp_job.Designation_Id',
                'designation.Designation_Id'
            );
            query.leftJoin(
                'department',
                'emp_job.Department_Id',
                'department.Department_Id'
            );
            query.leftJoin(
                'employee_type',
                'emp_job.EmpType_Id',
                'employee_type.EmpType_Id'
            );
            query.leftJoin(
                'location',
                'emp_job.Location_Id',
                'location.Location_Id'
            );
            query.where(async db => {
              if (isManager || isServiceProviderAdmin || !isAdmin) {
                db.whereIn('emp_job.Employee_Id', subEmployeeIds)
              }
              db.whereNotIn(
                  'emp_job.Employee_Id',
                  alreadyAppliedOrApprovedEmployeeIds
              );
              db.andWhereNot('emp_job.Employee_Id', envelope.loggedInUserId)
            });
    
            if (filter.searchValue && filter.searchValue.length > 0) {
              query.where(db => {
                db.where(
                    'emp_personal_info.Emp_First_Name',
                    'like',
                    `%${filter.searchValue}%`
                ); // employee first name
                db.orWhere(
                    'emp_personal_info.Emp_Last_Name',
                    'like',
                    `%${filter.searchValue}%`
                ); // employee last name
                db.orWhere(
                  organizationDbConnection.raw("concat_ws(' ',emp_personal_info.Emp_First_Name,emp_personal_info.Emp_Last_Name)"),
                  'like',
                  `%${filter.searchValue}%`
                );//employee first and last name
                db.orWhere('emp_personal_info.Employee_Id', 'like', `%${filter.searchValue}%`);// employee id
                db.orWhere('emp_job.User_Defined_EmpId', 'like', `%${filter.searchValue}%`);// user defined employee id
              })
            }
            if (filter.status && filter.status.length > 0) {
              query.where(db => {
                db.whereIn('emp_job.Emp_Status', filter.status)
              })
            }
            if (filter.designationId && filter.designationId > 0) {
              query.where('emp_job.Designation_Id ', filter.designationId)
            }
            if (filter.departmentId && filter.departmentId > 0) {
              query.where('emp_job.Department_Id', filter.departmentId)
            }
            if (filter.employeeTypeId && filter.employeeTypeId > 0) {
              query.where('emp_job.EmpType_Id', filter.employeeTypeId)
            }
            if (filter.locationId && filter.locationId > 0) {
              query.where('emp_job.Location_Id', filter.locationId)
            }
            // Append where conditon check the employee Form_Status = 1 or not
            query.where('emp_personal_info.Form_Status',1);
    
            return query
            .then(async result => {
              response.result = result.map(async row => {
                return {
                  ...row,
                  photoPath: row.photoPath
                      ? await Common.getFileURL(
                          envelope.orgCode,
                          Common.constant.awsImageBucket,
                          row.photoPath
                      )
                      : null
                }
              });
    
              /** Get employee relieving reasons */
              let employeeRelievingReason = await Common.getEmployeeRelievingReaason(organizationDbConnection);
    
              /** Return the relieving reason details */
              response.relievingReasonDetails = employeeRelievingReason;
              return response
            })
            .catch(function(error) {
              return {
                error: Common.getError(error, 'ERE0011')
              }
            })
            .finally(function(){
              // destroy database connection
              organizationDbConnection.destroy();
            })
          }
        }else{
          console.log('Empty response from the checkEmployeeAccessRights() in the getAllEmployees() function.',checkAccessRights);
          throw('_DB0100');
        }
      } catch (error) {
        console.log('Error in the getAllEmployees() function main catch block.',error);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        response.error = Common.getError(error, 'ERE0107');
        return response
      }
    },

    /**
     * For get all designation list for drop down
     */
    getAllDesignation: async (parent, args, root) => {
      const { envelope } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        const { error, result: designations } = await Common.getAllDesignation(
            organizationDbConnection
        );
        if (error) { response.error = error;}
        else { response.result = designations; }
        // destroy database connection
        organizationDbConnection.destroy();
        // return response to UI
        return response
      } catch (error) {
        response.error = Common.getError(error, '_DB0004');
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },

    /**
     * For get all department list for drop down
     */
    getAllDepartment: async (parent, args, root) => {
      const { envelope } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        const { error, result: departments } = await Common.getAllDepartment(
            organizationDbConnection
        );
        if (error) { response.error = error; }
        else  { response.result = departments; }
        // destroy database connection
        organizationDbConnection.destroy();
        // return response to UI
        return response
      } catch (error) {
        response.error = Common.getError(error, '_DB0005');
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },

    /**
     * For get all employee type list for drop down
     */
    getAllEmployeeType: async (parent, args, root) => {
      const { envelope } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        const {
          error,
          result: employeeTypes
        } = await Common.getAllEmployeeType(organizationDbConnection);
        if (error) { response.error = error; }
        else { response.result = employeeTypes; }
        // destroy database connection
        organizationDbConnection.destroy();
        // return response to UI
        return response
      } catch (error) {
        response.error = Common.getError(error, '_DB0006');
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },

    /**
     * For get all employee type list for drop down
     */
    getAllLocation: async (parent, args, root) => {
      const { envelope } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        const { error, result: locations } = await Common.getAllLocation(
            organizationDbConnection
        );
        if (error) { response.error = error; }
        else { response.result = locations; }
        // destroy database connection
        organizationDbConnection.destroy();
        // return response to UI
        return response
      } catch (error) {
        response.error = Common.getError(error, '_DB0007');
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },

    /**
     * For get particular employee resignation detail
     * `args` - (envelope, employeeId)
     */
    getEmployeeCurrentResignation: async (parent, args, root) => {
      const { envelope } = args;
      const response = await Helper.getEmployeeResignation(args, [
        Common.constant.statusApplied,
        Common.constant.statusApproved,
        Common.constant.statusIncomplete
      ]);

      if (response.error) {
        return response
      } else {
        response.result = {
          resignation: response.result
        }
      }

      // check resignation has mapped to workflow
      // if workflowInstanceId available means, workflow is mapped to this resignation.
      if (
          response.result.resignation &&
          response.result.resignation.workflowInstanceId
      ) {
        // check workflow status
        // get mapped dynamic form id from first user task(employee task)
        response.result['workflow'] = {
          workflowInstanceId: response.result.resignation.workflowInstanceId
        };

        const {
          result: dynamicFormDetailResult,
          error: dynamicFormDetailError
        } = await Helper.getResignationDynamicFormDetail(
            envelope.orgCode,
            response.result.resignation.workflowInstanceId,
            response.result.resignation.employeeId
        );

        if (!dynamicFormDetailError) {
          response.result['workflow']['workflowTaskId'] =
              dynamicFormDetailResult.workflowTaskId;
          if (dynamicFormDetailResult.dynamicFormTemplate) {
            response.result['dynamicFormTemplate'] =
                dynamicFormDetailResult.dynamicFormTemplate
          }

          // get already saved form response details
          const {
            error: dynamicFormResponseError,
            result: dynamicFormResponseResult
          } = await Helper.getDynamicFormResponse(
              envelope.orgCode,
              dynamicFormDetailResult.workflowTaskId
          );

          if (dynamicFormResponseError) {
            response.error = Common.getError(
                dynamicFormResponseError,
                'ERE0009'
            )
          } else {
            response.result['dynamicFormResponse'] = dynamicFormResponseResult
          }
        }
      }
      return response
    },

    /**
     * For get dynamic form template & response details
     */
    getWorkflowTaskDynamicFormDetails: async (parent, args, root) => {
      const { envelope, dynamicFormId, workflowTaskId } = args;
      let response = { error: null, result: {} };

      const {
        error: dynamicFormTemplateError,
        result: dynamicFormTemplateResult
      } = await Helper.getDynamicFormTemplate(envelope.orgCode, dynamicFormId);

      if (dynamicFormTemplateError) {
        response.error = Common.getError(dynamicFormTemplateError, 'ERE0008')
      } else {
        response.result.dynamicFormTemplates = dynamicFormTemplateResult
      }

      const {
        error: dynamicFormResponseError,
        result: dynamicFormResponseResult
      } = await Helper.getDynamicFormResponse(envelope.orgCode, workflowTaskId);

      if (dynamicFormResponseError) {
        response.error = Common.getError(dynamicFormResponseError, 'ERE0009')
      } else {
        response.result['dynamicFormResponse'] = dynamicFormResponseResult
      }

      return response
    },

    /**
     * For get particular employee resignation detail
     * `args` - (envelope, employeeId)
     */
    getEmployeeResignation: async (parent, args, root) => {
      return await Helper.getEmployeeResignation(args, [])
    },

    /**
     * For get all employee resignation detail with filters
     * `args` - (envelope, filter)
     */
    getAllResignation: async (parent, args, root) => {
      const { envelope, filter } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection='';
      try {
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        // to check rights of the employee.
        accessRights = await Common.checkEmployeeAccessRights(
          organizationDbConnection,
          envelope.loggedInUserId,
          null,
          '',
          'ui',
          false,
          envelope.formId ? envelope.formId: Common.constant.formIdResignation
        );
        
        if (Object.keys(accessRights).length > 0) {
          if(accessRights.error){
            console.log('Error from the checkEmployeeAccessRights() in the getAllResignation() function.',accessRights);
            response.error = accessRights.error
                ? accessRights.error
                : Common.getError(null, '_DB0100');
            // destroy database connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return response;
          }else{
            let isManager = false;
            let isAdmin = false;
            let isServiceProviderAdmin = false;
            accessRights = JSON.parse(accessRights);
            
            if(accessRights.Employee_Role !== 'admin'){
                /** If the login emp is not an admin then check whether they are service provider admin or not. 
                 * if the employee is not a service provider admin then check whether the emp is manager or not */
                let serviceProviderFormAccessRights = await Common.checkEmployeeAccessRights(
                  organizationDbConnection,
                  envelope.loggedInUserId,
                  Common.constant.formServiceProviderAdmin,
                  '',
                  'ui');

                  if (Object.keys(serviceProviderFormAccessRights).length > 0) {
                    if(serviceProviderFormAccessRights.error){
                      console.log('Error when getting service provider accessrights in the getAllResignation() function.',accessRights);
                      response.error = serviceProviderFormAccessRights.error
                          ? serviceProviderFormAccessRights.error
                          : Common.getError(null, '_DB0100');
                      // destroy database connection
                      organizationDbConnection ? organizationDbConnection.destroy() : null;
                      return response;
                    }else{
                      serviceProviderFormAccessRights = JSON.parse(serviceProviderFormAccessRights);
                      if(serviceProviderFormAccessRights.Role_Update){
                        isServiceProviderAdmin = true;
                      } else if(accessRights.Is_Manager){
                          isManager = true;
                      }
                    }
                  }else {
                    if(accessRights.Is_Manager){
                      isManager = true;
                    }
                  }
            }else{
              isAdmin = true;
            }
            // prepare organization chart query
            const query = organizationDbConnection.select(
                'er.Resignation_Id as resignationId',
                'er.Employee_Id as employeeId',
                organizationDbConnection.raw(
                    'CONCAT(epi1.Emp_First_Name, \' \', epi1.Emp_Last_Name) as "employeeName"'
                ),
                'epi1.Photo_Path as employeePhotoPath',
                'epi1.Gender as employeeGender',
                'epi1.Is_Manager as employeeIsManager',
                'ej.User_Defined_EmpId as userDefinedEmpId',
                'ej.Designation_Id as employeeDesignationId',
                'ds.Designation_Name as employeeDesignationName',
                'ej.Department_Id as employeeDepartmentId',
                'd.Department_Name as employeeDepartmentName',
                'er.Approver_Id as approverId',
                organizationDbConnection.raw(
                    'CONCAT(epi2.Emp_First_Name, \' \', epi2.Emp_Last_Name) as "approverName"'
                ),
                'er.Approval_Status as resignationStatus',
                'er.Notice_Date as appliedDate',
                'er.Resignation_Date as resignationDate',
                'er.Workflow_Instance_Id as workflowInstanceId',
                'er.Workflow_Status as workflowStatus',
                'er.Added_On as addedOn',
                'er.Added_By as addedUserId',
                organizationDbConnection.raw(
                    'CONCAT(epi3.Emp_First_Name, \' \', epi3.Emp_Last_Name) as "addedUserName"'
                ),
                'er.Updated_On as updatedOn',
                'er.Updated_By as updatedUserId',
                organizationDbConnection.raw(
                    'CONCAT(epi4.Emp_First_Name, \' \', epi4.Emp_Last_Name) as "updatedUserName"'
                ),
                'er.Reason_Id as reasonId',
                'esr.ESIC_Reason as esicReasonName',
                'er.Withdrawn_Cancellation_Comment as withdrawnCancellationComment',
                'er.Relieving_Reason_Comment as relievingReasonComment',
                'er.File_Name as fileName',
                'location.Location_Id as locationId','location.Location_Name as locationName',
                'employee_type.Employee_Type as employeeType','employee_type.EmpType_Id as empTypeId'
            );
            query.from('emp_resignation AS er');
            query.leftJoin(
                'emp_personal_info as epi1',
                'er.Employee_Id',
                'epi1.Employee_Id'
            );
            query.leftJoin('emp_job as ej', 'er.Employee_Id', 'ej.Employee_Id');
            query.leftJoin(
                'department as d',
                'ej.Department_Id',
                'd.Department_Id'
            );
            query.leftJoin(
                'designation as ds',
                'ej.Designation_Id',
                'ds.Designation_Id'
            );
            query.leftJoin(
                'emp_personal_info as epi2',
                'er.Approver_Id',
                'epi2.Employee_Id'
            );
            query.leftJoin(
                'emp_personal_info as epi3',
                'er.Added_By',
                'epi3.Employee_Id'
            );
            query.leftJoin(
                'emp_personal_info as epi4',
                'er.Updated_By',
                'epi4.Employee_Id'
            );
          query.leftJoin(
            'esic_reason as esr',
            'er.Reason_Id',
            'esr.Reason_Id'
          );
          query.leftJoin(
            'location',
            'ej.Location_Id',
            'location.Location_Id'
        );
        query.leftJoin(
          'employee_type',
          'ej.EmpType_Id',
          'employee_type.EmpType_Id'
      );
            if (filter.searchValue && filter.searchValue.length > 0) {
              query.where(db => {
                db.where('epi1.Emp_First_Name', 'like', `%${filter.searchValue}%`); // employee first name
                db.orWhere(
                    'epi1.Emp_Last_Name',
                    'like',
                    `%${filter.searchValue}%`
                ); // employee last name
                db.orWhere(
                  organizationDbConnection.raw("concat_ws(' ',epi1.Emp_First_Name,epi1.Emp_Last_Name)"),
                  'like',
                  `%${filter.searchValue}%`
                );// employee first and last name
                db.orWhere('epi1.Employee_Id', 'like', `%${filter.searchValue}%`);// employee id
                db.orWhere('ej.User_Defined_EmpId', 'like', `%${filter.searchValue}%`);// user defined employee id
                db.orWhere(
                    'epi2.Emp_First_Name',
                    'like',
                    `%${filter.searchValue}%`
                ); // approver first name
                db.orWhere(
                    'epi2.Emp_Last_Name',
                    'like',
                    `%${filter.searchValue}%`
                ); // approver last name
                db.orWhere(
                  organizationDbConnection.raw("concat_ws(' ',epi2.Emp_First_Name,epi2.Emp_Last_Name)"),
                  'like',
                  `%${filter.searchValue}%`
                );//approver first and last name
              })
            }
            if (filter.designationId && filter.designationId > 0) {
              query.where('ej.Designation_Id ', filter.designationId)
            }
            if (filter.departmentId && filter.departmentId > 0) {
              query.where('ej.Department_Id', filter.departmentId)
            }
            if (
                filter.noticeDate &&
                filter.noticeDate.start &&
                filter.noticeDate.end
            ) {
              query.andWhereBetween('er.Notice_Date', [
                filter.noticeDate.start,
                filter.noticeDate.end
              ])
            }
            if (
                filter.resignationDate &&
                filter.resignationDate.start &&
                filter.resignationDate.end
            ) {
              query.andWhereBetween('er.Resignation_Date', [
                filter.resignationDate.start,
                filter.resignationDate.end
              ])
            }
            if (filter.status && filter.status.length > 0) {
              query.where(db => {
                db.whereIn('er.Approval_Status', filter.status)
              })
            }
            if (filter.employees && filter.employees.length > 0) {
              query.where(db => {
                db.whereIn('er.Employee_Id', filter.employees)
              })
            } else if (isManager) {
              // for fetch manager level reporting employees list
              const subEmployeeIds = await Helper.getAllSubEmployee(
                  organizationDbConnection,
                  envelope.loggedInUserId
              );
              query.where(db => {
                db.whereIn('er.Employee_Id', subEmployeeIds)
              })
            } else if (isServiceProviderAdmin){
              // fetch employees associated with the service provider
              const serviceProviderId = await Helper.getServiceProviderId(
                organizationDbConnection,
                envelope.loggedInUserId
              );
              
              const subEmployeeIds = await Helper.getAllServiceProviderEmployees(
                organizationDbConnection,
                serviceProviderId
              );
              
              query.where(db => {
                db.whereIn('er.Employee_Id', subEmployeeIds)
              })
            } else if(!isAdmin) {
              query.where(db => {
                db.where('er.Employee_Id', envelope.loggedInUserId)
              })
            }

            query.orderBy('er.Added_On', 'desc');
            query.limit(filter.limit);
            query.offset(filter.offset);

            return query
            .then(async result => {
              response.result = result.map(async row => {
                return {
                  ...row,
                  employeePhotoPath: row.employeePhotoPath
                      ? await Common.getFileURL(
                          envelope.orgCode,
                          Common.constant.awsImageBucket,
                          row.employeePhotoPath
                      )
                      : null,
                  appliedDate: await Helper.formDate(new Date(row.appliedDate).toDateString()),
                  resignationDate: await Helper.formDate(new Date(row.resignationDate).toDateString()),
                  addedOn: Common.getFormattedDateString(
                      null,
                      Common.constant.YYYYMMDDHHmmss,
                      row.addedOn
                  ),
                  updatedOn: row.updatedOn
                      ? Common.getFormattedDateString(
                          null,
                          Common.constant.YYYYMMDDHHmmss,
                          row.updatedOn
                      )
                      : null
                }
              });
              return response
            })
            .catch(function(error) {
              console.log('Error in the getAllResignation() function .catch block.',error);
              response.error = Common.getError(error, 'ERE0003');
              return response;
            })
            .finally(function() {
              organizationDbConnection.destroy();
            })
          }
        } else {
          console.log('Empty response from the checkEmployeeAccessRights() in the getAllResignation() function.',accessRights);
          throw('_DB0100');
        }
      } catch (error) {
        console.log('Error in the getAllResignation() function main catch block.',error);
        //destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        response.error = Common.getError(error, 'ERE0103');
        return response
      }
    },

    /**
     * For fetch resignation status
     */
    getResignationStatus: async (parent, args, root) => {
      const { envelope, resignationId } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );
        // to check rights of the employee.
        accessRights = await Common.checkEmployeeAccessRights(
          organizationDbConnection,
          envelope.loggedInUserId,
          null,
          process.env.roleView,
          'Back-end',
          false,
          envelope.formId ? envelope.formId: Common.constant.formIdResignation
        );
        if (accessRights && !accessRights.error) {
          const query = organizationDbConnection.select(
              'Notice_Date as appliedDate',
              'Resignation_Date as resignationDate',
              'Approval_Status as status',
              'Workflow_Instance_Id as workflowInstanceId',
              'Workflow_Status as workflowStatus'
          );
          query.from('emp_resignation');
          query.where('Resignation_Id', resignationId);
          return query
              .then(async result => {
                if (result[0]) {
                  const { status, workflowInstanceId, workflowStatus, appliedDate, resignationDate  } = result[0];
                  if (workflowInstanceId && workflowInstanceId.length > 0) {
                    response.result = {
                      status: workflowStatus,
                      appliedDate: appliedDate,
                      resignationDate: resignationDate,
                      workflowInstanceId: workflowInstanceId
                    };
                    return response
                  } else {
                    response.result = {
                      status: status,
                      appliedDate: appliedDate,
                      resignationDate: resignationDate,
                      workflowInstanceId: workflowInstanceId
                    };
                    return response
                  }
                } else {
                  return response
                }
              })
              .catch(error => {
                response.error = Common.getError(error, 'ERE0012');
                return response
              })
              .finally(function() {
                organizationDbConnection.destroy();
              })
        } else {
          response.error = accessRights.error
              ? accessRights.error
              : Common.getError(null, '_DB0100');
          return response
        }
      } catch (error) {
        response.error = Common.getError(error, 'ERE0106');
        // destory database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },

    getEmployeeNotificationPeriodDays: async (parent, args, root) => {
      const {envelope, employeeId} = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        const {
          error,
          result: {noticePeriodDays}
        } = await Helper.getEmployeeNotificationPeriodDays(organizationDbConnection, employeeId);
        if (error) {
          response.error = Common.getError(error, 'ERE0012');
        } else {
          response.result = parseInt(noticePeriodDays);
        }
        // destory database connection
        organizationDbConnection.destroy();
      } catch (e) {
        response.error = Common.getError(e, 'ERE0012');
        // destory database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
      }
      return response
    }
  },
  Mutation: {
    /**
     * For create a new resignation details
     * `args` - (envelope, employeeId)
     */
    createResignation: async (parent, args, root) => {
      console.log('Inside the createResignation() function.',args);
      let { envelope, employeeId, resignationDate, appliedDate, reasonId, esicReason, relievingReasonComment, fileName } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        // to check rights of the employee.
        accessRights = await Common.checkEmployeeAccessRights(
          organizationDbConnection,
          envelope.loggedInUserId,
          null,
          process.env.roleAdd,
          'Back-end',
          false,
          envelope.formId ? envelope.formId: Common.constant.formIdResignation
        );

        if (accessRights && !accessRights.error) {
          let timeZone = await Common.getEmployeeTimeZone(
              envelope.loggedInUserId,
              organizationDbConnection
          );

          // if user timeZone available then allow the user to create or update the record
          if (timeZone && !timeZone.error) {
            const {
              error,
              result: { designationId, noticePeriodDays }
            } = await Helper.getEmployeeNotificationPeriodDays(organizationDbConnection, employeeId);
            if (error) {
              response.error = Common.getError(error);
              // destroy database connection
              organizationDbConnection.destroy();
              return response
            }

            const resignationStatus = Common.constant.statusApplied;

            const validation = validateResignationDate(resignationDate, appliedDate, relievingReasonComment);
            if (validation.length) {
              console.log('Validation error in the createResignation() function.',validation);
              response.result = {
                success: false,
                message: `Unable to create resignation`,
                validation: validation
              };
              // destroy database connection
              organizationDbConnection.destroy();
              return response;
            }

            const get = {
              designationBasedWorkflows: transaction => {
                // check employee have any workflow based on designation
                return new Promise(resolve => {
                  const query = transaction.select(
                      'w.Event_Id as eventId',
                      'w.Workflow_Unique_Id as workflowUniqueId',
                      'w.Default_workflow as isDefaultWorkflow',
                      'tw.ui_config'
                  );
                  query.from('workflows as w');
                  query.leftJoin(
                      'workflow_designation as wd',
                      'w.Workflow_Id',
                      'wd.Workflow_Id'
                  ); 
                  query.leftJoin(
                    'ta_workflow as tw',
                    'w.Event_Id',
                    'tw.event_id'
                  );
                  query.where(
                      'w.Workflow_Module_Id',
                      '=',
                      Common.constant.resignationWorkflowModuleId
                  ); // resignation module workflow
                  query.andWhere('wd.Designation_Id', designationId);

                  query
                      .then(result => {
 
                        resolve({
                          result: result.length > 0 ? result[0] : null
                        })
                      })
                      .catch(error => {
                        resolve({
                          error
                        })
                      })
                })
              },
              defaultWorkflow: transaction => {
                // need to check resignation module have any default workflow
                return new Promise(resolve => {
                  const query = transaction.select(
                      'w.Event_Id as eventId',
                      'w.Workflow_Unique_Id as workflowUniqueId',
                      'w.Default_workflow as isDefaultWorkflow',
                      'tw.ui_config'
                  );
                  query.from('workflows as w');
                  query.leftJoin(
                    'ta_workflow as tw',
                    'w.Event_Id',
                    'tw.event_id'
                  );
                  query.where(
                      'w.Workflow_Module_Id',
                      '=',
                      Common.constant.resignationWorkflowModuleId
                  ); // resignation module workflow
                  query.andWhere('w.Default_workflow', '=', 1);
                  query
                      .then(result => {
 
                        resolve({
                          result: result.length > 0 ? result[0] : null
                        })
                      })
                      .catch(error => {
                        resolve({
                          error
                        })
                      })
                })
              },
              updateWorkflowInstanceInResignation: (
                  transaction,
                  resignationId,
                  workflowInstanceId
              ) => {
                return new Promise(resolve => {
                  organizationDbConnection('emp_resignation')
                      .transacting(transaction)
                      .where('Resignation_Id', '=', resignationId)
                      .update({
                        Workflow_Instance_Id: workflowInstanceId,
                        Workflow_Status:
                        Common.constant.statusWaitingForApproval
                      })
                      .then(result => {
                        resolve({
                          result: result
                        })
                      })
                      .catch(function(error) {
                        console.log('Update workflow instance id error',error);
                        resolve({ error: error })
                      })
                })
              },
              // get the workflow configured data using eventId
              getResignationStatusBasedWorkflowData: (tmpEventId, transaction) =>{
                  return(
                    organizationDbConnection
                    .select('ta_workflow_version.ui_config','ta_workflow_version.created_date')
                    .from('ta_workflow_version')
                    .leftJoin('ta_workflow', 'ta_workflow.workflow_id','ta_workflow_version.workflow_id')
                    .leftJoin('ta_event', 'ta_workflow.event_id','ta_event.event_id')
                    .leftJoin('workflows', 'ta_event.event_id','workflows.Event_Id')
                    .where('workflows.Event_Id', tmpEventId)
                    .orderBy('ta_workflow_version.created_date','desc')
                    .transacting(transaction)
                    .then(workflowInfo =>{

                      // check length of the workflowInfo
                      if (workflowInfo.length){

                        //orderedby desc so we have to get the latest one so get the 0th position data
                        let uiConfigData = workflowInfo[0].ui_config

                        // variable declarations
                        let status = 'Applied';

                        // parse and get the results
                        let parsedWorkflowData = JSON.parse(uiConfigData);
                        // get the workflow start node of the workflow
                        const workFlowStartNode = parsedWorkflowData.startNode;
                        // get the workflow nodes. Each node considered as a task
                        const workflowNodes = parsedWorkflowData.nodes;
                        // From the workflow start node we can get the next node id
                        const workflowNextNodeId = parsedWorkflowData.nodes[workFlowStartNode].next[0];

                        // We are going to check only the first node. In the first node check form is exist also that is associated with the initiator or not. 
                        // If yes the resignation status will be Incomplete. Once the form is filled and submited then the status will be Applied.
                        // get the associated form if exists
                        let workFlowNodes = workflowNodes[workflowNextNodeId].data.modalTaskData;
                        if (workFlowNodes && Object.keys(workFlowNodes).length > 0){
                        let isFormExists = workFlowNodes.formId ? workFlowNodes.formId : null;
                        
                        // get the user option of the corresponding node
                        let checkUserOption = workFlowNodes.userOption ? workFlowNodes.userOption : "";
                        
                        // check form is exist also that is associated with the initiator or not. If yes the resignation status will be Incomplete.
                        // This has to be check for the first node only. If form is exist then it will not be '0'. If not exist then it will be '0'
                        if (isFormExists.id != '0' && checkUserOption === 'initiator') {
                          status = 'Incomplete';
                        }
                      }
                        // return response back to function
                        return status;
                      }else{
                        console.log('Error in getResignationStatusBasedWorkflowData() function else block.');
                        // throw error
                        throw ('ERE0001');
                      }                      
                    })
                    .catch(getWorkflowDataError=>{
                      console.log('Error in getResignationStatusBasedWorkflowData() function .catch block'.getWorkflowDataError);
                      throw(getWorkflowDataError);
                    })
                  )
              }
            };

            const services = {
              initiateWorkflow: (eventId, instanceData) => {
                const apiHeaders={
                  org_code: envelope.orgCode,
                  employee_id: envelope.loggedInUserId,
                  db_prefix: process.env.dbPrefix
                }
                const config = {
                  method: 'post', 
                  url: `${process.env.workflowEngineUrl}/workflow/initiate`,
                  maxBodyLength: Infinity,
                  data : JSON.stringify({
                    event_id: eventId,
                    instance_data: instanceData
                  }),
                  headers: apiHeaders
              };
              console.log("Inside callingSunfishesAPI request ", config);
                return new Promise(resolve => {
                  // initiate the workflow with Workflow Engine Initiate API's
                  axios.request(config)
                      .then(function(response) {
                        resolve({
                          result: response
                        })
                      })
                      .catch(function(error) {
                        resolve({ error: error })
                      })
                })
              }
            };

            // create resignation entry
            return organizationDbConnection
                .transaction(function(transaction) {
                  // get the resignation records for the employee
                  return organizationDbConnection('emp_resignation')
                  .count('* as recordExistCount')
                  .where('Employee_Id',employeeId)
                  .whereIn('Approval_Status', [Common.constant.statusApplied, Common.constant.statusApproved, Common.constant.statusIncomplete])
                  .transacting(transaction)
                  .then(async (isRecordExists) =>{   
                    // check if resignation record already exists or not
                    if (isRecordExists.length > 0 && isRecordExists[0].recordExistCount === 0){  

                      let resignationWorkflow = null;
                      // get designation based workflow
                      const {
                        error: designationBasedWorkflowError,
                        result: designationBasedWorkflowResult
                      } = await get.designationBasedWorkflows(transaction);

                      if (designationBasedWorkflowError) {
                        response.error = Common.getError(
                          designationBasedWorkflowError
                        );
                        return response
                      } else {
                        resignationWorkflow = designationBasedWorkflowResult
                      }

                      // if designation based workflow not exist then get the default workflow
                      if (!resignationWorkflow) {
                        // check resignation module have any default workflow
                        let {
                          error: defaultWorkflowError,
                          result: defaultWorkflowResult
                        } = await get.defaultWorkflow(transaction);

                        if (defaultWorkflowError) {
                          response.error = Common.getError(error);
                          return response
                        } else {
                          resignationWorkflow = defaultWorkflowResult
                        }
                      }

                    // If workflow available means trigger and initiate the workflow api.
                    if (resignationWorkflow && resignationWorkflow.eventId) {
                      if(resignationWorkflow.ui_config){
                        let isManagerExists = 0;
                        if(resignationWorkflow.ui_config.includes("assign_manager_at_runtime") || resignationWorkflow.ui_config.includes("assign_secondline_manager_at_runtime")){
                          isManagerExists = await organizationDbConnection('emp_job')
                                .select('Manager_Id')
                                .where('Employee_Id',employeeId)
                                .then(async (isManagerExists) =>{
                                  if(isManagerExists.length > 0){
                                    return isManagerExists[0].Manager_Id ? 1 : 0;
                                  } 
                                  return 0;
                                }).catch(function(error) {
                                  console.log('Error in getmanager for the employee .catch block',error);
                                  response.error = Common.getError(error, 'ERE0126');
                                  return response
                                })

                        } else {
                          isManagerExists = 1;
                        }
                        
                        if(isManagerExists){
                          // function to get workflow based resignation status
                          let resignationStatus = await get.getResignationStatusBasedWorkflowData(resignationWorkflow.eventId, transaction);

                          let createResignationInputs = {
                            Employee_Id: employeeId,
                            Approver_Id: 0,
                            Approval_Status: resignationStatus,
                            Notice_Date: appliedDate,
                            Resignation_Date: resignationDate,
                            File_Name: fileName,
                            Relieving_Reason_Comment: relievingReasonComment,
                            Added_On: Common.getFormattedDateString(
                                timeZone,
                                Common.constant.YYYYMMDDHHmmss
                            ),
                            Added_By: envelope.loggedInUserId
                          };
                          console.log("createResignationInputs", createResignationInputs);
                          /** If reason id exist */
                          if(reasonId){
                            createResignationInputs.Reason_Id = reasonId;
                          }

                          // insert resignation record if resignation not exists for the employee                       
                          return organizationDbConnection('emp_resignation')
                            .transacting(transaction)
                            .insert(createResignationInputs)
                            .then(async data => {
                              const resignationId = data[0];

                              const returnValues = {
                                success: true,
                                message: `Created successfully`,
                                resignation: {
                                  employeeId: employeeId,
                                  resignationId: resignationId,
                                  resignationDate: resignationDate,
                                  fileName: fileName,
                                  appliedDate: appliedDate,
                                  resignationStatus: resignationStatus
                                },
                                dynamicFormTemplate: null,
                                workflow: null,
                                validation: null
                              };
                              
                                console.log('resignationId',resignationId);
                                // initiate workflow.
                                let {
                                  error: initiateWorkflowError,
                                  result: initiateWorkflowResult
                                } = await services.initiateWorkflow(
                                    resignationWorkflow.eventId,
                                    {
                                      resignationId: resignationId,
                                      employeeId: employeeId,
                                      orgCode: envelope.orgCode,
                                      isSelfApply:
                                          envelope.loggedInUserId === employeeId ? 1 : 0,
                                      formName: Common.constant.formResignation,
                                      formId: Common.constant.formIdResignation,
                                      appliedDate: appliedDate,
                                      exitDate: resignationDate,
                                      fileName: fileName,
                                      esicReason: esicReason ? esicReason : "",
                                      reasonId : parseInt(reasonId),
                                      addedOn: Common.getFormattedDateString( timeZone,Common.constant.YYYYMMDDHHmmss),
                                      initiatorId: envelope.loggedInUserId,
                                      updatedOn: "",
                                      updatedBy:0,
                                      approvedOn: "",
                                      approvedBy:0,
                                      approvalStatus:resignationStatus,
                                      relievingReasonComment: relievingReasonComment,
                                      withdrawnCancellationComment : "",
                                    }
                                );
                                
                                if (initiateWorkflowError) {
                                  console.log(
                                      `Error while initiate workflow, Status Code: ${initiateWorkflowError.response.status}, Status Text: ${initiateWorkflowError.response.statusText}, Message: ${initiateWorkflowError.response.data.message}`
                                  );
                                  throw 'ERE0010'
                                }

                                const workflowProcessInstanceId =
                                    initiateWorkflowResult.data.workflowProcessInstanceId;
                                
                                if (workflowProcessInstanceId) {
                                  
                                  returnValues['workflow'] = {
                                    workflowInstanceId: workflowProcessInstanceId
                                  };
                                  returnValues.resignation.resignationStatus =
                                      Common.constant.statusWaitingForApproval;
                                  // store workflow_instance_id in resignation table
                                  const {
                                    error: updateWorkflowInstanceInResignationError
                                  } = await get.updateWorkflowInstanceInResignation(
                                      transaction,
                                      resignationId,
                                      workflowProcessInstanceId
                                  );

                                  if (updateWorkflowInstanceInResignationError) {
                                    console.log(
                                        `Error while update the workflow process instance values, resignationId: ${resignationId}, workflowProcessInstanceId: ${workflowProcessInstanceId}, error:${updateWorkflowInstanceInResignationError}`
                                    )
                                  }

                                  const {
                                    result: dynamicFormDetailResult,
                                    error: dynamicFormDetailError
                                  } = await Helper.getResignationDynamicFormDetail(
                                      envelope.orgCode,
                                      workflowProcessInstanceId,
                                      envelope.loggedInUserId
                                  );

                                  if (!dynamicFormDetailError) {
                                    returnValues.workflow['workflowTaskId'] =
                                        dynamicFormDetailResult.workflowTaskId;
                                    if (dynamicFormDetailResult.dynamicFormTemplate) {
                                      returnValues['dynamicFormTemplate'] =
                                          dynamicFormDetailResult.dynamicFormTemplate
                                    }
                                  }
                                }
                              await Common.createSystemLogActivities({
                                action: process.env.systemlogAdd,
                                userIp: root.userIp,
                                employeeId: envelope.loggedInUserId,
                                formName: Common.constant.formResignation,
                                trackingColumn: 'Resignation Id',
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: resignationId
                              });
                              response.result = returnValues;
                              return response
                            })
                            .then(transaction.commit)
                            .catch(function(error) {
                              console.log(
                                  'Error while run create resignation details scripts',
                                  error
                              );
                              throw error
                            })
                        } else{
                          console.log('Manager is not assigned for the employee');
                          // throw error if no workflow exists
                          throw ('ERE0128')
                        }   
                      } else{
                        console.log('Workflow is not configured');
                        // throw error if no workflow exists
                        throw ('ERE0127')
                      }
                    }else{
                      console.log('Workflow does not exist.',resignationWorkflow);
                      // throw error if no workflow exists
                      throw ('ERE0001')
                    }
                    }else{
                      console.log('Resignation already exist.',isRecordExists);
                      // throw error if the employee has a resignation record
                      throw ('ERE0112')
                    }
                  })
                })
                .then(function(result) {
                  return result
                })
                .catch(function(error) {
                  console.log('Error in createResignation() function .catch block',error);
                  response.error = Common.getError(error, 'ERE0001');
                  return response
                })
                .finally(function(){
                  // destroy database connection
                  organizationDbConnection.destroy();
                })
          } else {
            console.log('Error or empty response returned while getting login employee timezone.',timeZone);
            response.error = timeZone.error;
            // destroy database connection
            organizationDbConnection.destroy();
            return response
          }
        } else {
          console.log('Login employee does not have an access.',accessRights);
          response.error = accessRights.error
              ? accessRights.error
              : Common.getError(null, '_DB0101');
          // destroy database connection
          organizationDbConnection.destroy();
          return response
        }
      } catch (error) {
        console.log('Error in the createResignation() function main catch block.',error);
        response.error = Common.getError(error, 'ERE0101');
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },

    /**
     * For update a particular resignation details
     * `args` - (envelope, resignationId, resignationDate)
     */
    updateResignationDate: async (parent, args, root) => {
      console.log('Inside updateResignationDate function.',args);
      // require moment timezone
      let moment = require('moment-timezone');
      let { envelope, resignationId, resignationDate, appliedDate,fileName } = args;
      let workflowInstanceId = "";
      let currentResignationDate = "";
      // get current date
      let currentDate = moment().format('YYYY-MM-DD');
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      let appManagerDbConnection;
      try {
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        // to check rights of the employee.
        accessRights = await Common.checkEmployeeAccessRights(
          organizationDbConnection,
          envelope.loggedInUserId,
          null,
          '',
          'ui',
          false,
          envelope.formId ? envelope.formId: Common.constant.formIdResignation
        );

        if (Object.keys(accessRights).length > 0){
          if(accessRights.error){
            console.log('Error from the checkEmployeeAccessRights() in the updateResignationDate() function.',accessRights);
            response.error = accessRights.error
                ? accessRights.error
                : Common.getError(null, '_DB0102');
            // destroy database connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return response
          }else {
            accessRights= JSON.parse(accessRights);
            if (accessRights.Role_Update === 0){
              throw('_DB0102');
            }
            else {
              let timeZone = await Common.getEmployeeTimeZone(
                  envelope.loggedInUserId,
                  organizationDbConnection
              );
              // get the current time based on mainbranch location
              let getOrgTimeZone = await commonLib.func.getMainBranchLocationTimeZone(organizationDbConnection);
              getOrgTimeZone ? getOrgTimeZone : 'Asia/Kolkata';
              // if user timeZone available then allow the user to create or update the record
              if (timeZone && !timeZone.error) {
                const validation = validateResignationDate(resignationDate, appliedDate);
                if (validation.length) {
                  response.result = {
                    success: false,
                    message: `Unable to update the resignation date`,
                    validation: validation
                  };
                  return response;
                }
                // get the chosen resignation status
                let recordStatus = await organizationDbConnection('emp_resignation').select('Approval_Status','Workflow_Instance_Id','Employee_Id', 'Resignation_Date').where('Resignation_Id', resignationId);
                
                // check record exist or not
                if (recordStatus.length){
                  currentResignationDate = moment.utc(recordStatus[0].Resignation_Date)
                  let parsedResignationDate = moment.utc(resignationDate, 'YYYY/MM/DD');
                  if(!currentResignationDate.isSame(parsedResignationDate) && parsedResignationDate.isAfter(currentDate)){
                    let resignationEmployeeId = recordStatus[0].Employee_Id
                    //Validate Employee Already Exist
                    let getPersonalInfoData = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empPersonalInfo,'Employee_Id', resignationEmployeeId)
                    getPersonalInfoData = getPersonalInfoData[0]

                    let commonCheck = {
                      "Emp_First_Name": getPersonalInfoData.Emp_First_Name,
                      "Emp_Last_Name": getPersonalInfoData.Emp_Last_Name,
                      "Nationality": getPersonalInfoData.Nationality,
                      "DOB": getPersonalInfoData.DOB,
                      "Blood_Group": getPersonalInfoData.Blood_Group,
                      "Marital_Status": getPersonalInfoData.Marital_Status,
                    }

                    let alreadyEmployeeExists = await organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
                    .select("*")
                    .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EP.Employee_Id")
                    .where(commonCheck)
                    .andWhere("EJ.Emp_Status", "Active")
                    .whereNot("EJ.Employee_Id", resignationEmployeeId)
          
                    //Check if the employee active already exists
                    if (alreadyEmployeeExists && alreadyEmployeeExists.length) {
                        //Throw employee already exists error
                        throw 'ESS0126'
                    }
                    
                    //Get the old employeeDetails
                    let oldJobDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empJob,'Employee_Id',resignationEmployeeId)
                    oldJobDetails = oldJobDetails[0]

                    //Get the old bankDetails
                    let oldBankDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empBankDetails,'Employee_Id',resignationEmployeeId)
                    oldBankDetails = oldBankDetails.map((el)=>el.Bank_Account_Number)

                    //Get the contactDetails
                    let oldContactDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.contactDetails,'Employee_Id',resignationEmployeeId)
                    oldContactDetails = oldContactDetails[0]

                    //Get the insuranceDetails
                    let oldInsuranceDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empInsurance, 'Employee_Id', resignationEmployeeId)
                    
                    //Validate Already Exists Value 
                    let failedData = await commonLib.func.validateEmployeeAlreadyExistsValues(organizationDbConnection, oldJobDetails.User_Defined_EmpId, oldJobDetails.Emp_Email, oldBankDetails, oldContactDetails, oldJobDetails.External_EmpId, resignationEmployeeId, getPersonalInfoData, oldJobDetails.Pf_PolicyNo, oldInsuranceDetails)
        
                    if(failedData && failedData.length){
                      throw {
                        errorCode: 'ESS0126',
                        failedData: failedData
                      }
                    }                  
                  }

                  workflowInstanceId = recordStatus[0].Workflow_Instance_Id;
                  
                  // get employeeId
                  let employeeId = recordStatus[0].Employee_Id;
                  // get the resignation status
                  recordStatus = recordStatus[0].Approval_Status;
                  let isEmployeeSettlementInitiated = await employeeSettlementInitiated(organizationDbConnection,employeeId);

                  if(isEmployeeSettlementInitiated === 0){
                  // Assign access into variable
                  let isAllowToEdit = 1;
                  // check if record is Approved Status
                  if (recordStatus === 'Approved'){
                    // if login employee is either admin or employee admin or super admin then resignation record can be edited
                    if(accessRights.Employee_Role.toLowerCase()==='admin'){
                      isAllowToEdit = 1;
                    }else{
                      /** If the login emp is not an admin then check whether they are service provider admin or not. 
                       * if the employee is a service provider admin then allow them to update */
                      let serviceProviderFormAccessRights = await Common.checkEmployeeAccessRights(
                        organizationDbConnection,
                        envelope.loggedInUserId,
                        Common.constant.formServiceProviderAdmin,
                        '',
                        'ui');

                        if (Object.keys(serviceProviderFormAccessRights).length > 0) {
                          if(serviceProviderFormAccessRights.error){
                            console.log('Error when getting service provider accessrights in the updateResignationDate() function.',accessRights);
                            response.error = serviceProviderFormAccessRights.error
                                ? serviceProviderFormAccessRights.error
                                : Common.getError(null, '_DB0100');
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return response;
                          }else{
                            serviceProviderFormAccessRights = JSON.parse(serviceProviderFormAccessRights);
                            if(serviceProviderFormAccessRights.Role_Update){
                              isAllowToEdit = 1;
                            } else {
                              isAllowToEdit = 0;
                            }
                          }
                        }
                    }
                  }
                  // check the isAllowToEdit flag is 1 or 0
                  if(isAllowToEdit === 1){
                    let resignationData = {
                      resignationId: resignationId,
                      orgCode: envelope.orgCode,
                      appliedDate: appliedDate,
                      exitDate: resignationDate,
                      updatedBy: envelope.loggedInUserId,
                      updatedOn : Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss),
                      approvalStatus: recordStatus,
                      fileName: fileName,
                    };
                    
                    let updateInstanceData = await updateWorkflowResignationInstanceData(organizationDbConnection, workflowInstanceId, resignationData);
                    let msg = "";
                    if(updateInstanceData){
                        // update the resignation record details
                        return organizationDbConnection('emp_resignation')
                          .where('Resignation_Id', '=', resignationId)
                          .update({
                            Notice_Date: appliedDate,
                            Resignation_Date: resignationDate,
                            Updated_On: Common.getFormattedDateString(
                                timeZone,
                                Common.constant.YYYYMMDDHHmmss
                            ),
                            File_Name: fileName,
                            Updated_By: envelope.loggedInUserId
                          })
                          .then(async () => {
                            // form system log params
                            let systemLogParams = {
                              action: process.env.systemlogEdit,
                              userIp: root.userIp,
                              employeeId: envelope.loggedInUserId,
                              formName: Common.constant.formResignation,
                              trackingColumn: 'Resignation Date: ' + resignationDate,
                              fileName: fileName,
                              organizationDbConnection: organizationDbConnection,
                              uniqueId: 'Resignation Id:' + resignationId
                            };
                            // If approval status is Approved then update the Emp_InActive_Date,Emp_Status in the emp_job table
                            if(recordStatus === 'Approved'){
                              // resignationDate date format from UI will be 2020/10/06 so before update have to change like 2020-10-06 format
                              resignationDate = resignationDate.split("/");
                              resignationDate = resignationDate[0] + '-' + resignationDate[1] + '-' + resignationDate[2];
                              // form input params to update in emp_job table
                              let updateJson = {
                                Emp_InActive_Date: resignationDate
                              };
                              
                              // Check if resignationDate is past date or not. if it is past date then InActive the employee and update default reason id
                              if (currentDate > resignationDate) {
                                updateJson.Emp_Status = 'InActive';
                                updateJson.Emp_Email = null;
                                updateJson.Reason_Id = Common.constant.defaultEsicReasonId;
                              }else if(!currentResignationDate.isSame(parsedResignationDate)){
                                //If the Exit date is past than the current date
                                updateJson.Emp_Status = 'Active';
                                updateJson.Reason_Id = Common.constant.defaultEsicReasonId;
                              }
                              await organizationDbConnection('emp_job')
                              .update(updateJson)
                              .where('Employee_Id', employeeId)
                              .then(async(updateEmpStatus) => {
                                // form update params
                                let updateParams={
                                  Member_Status : 'Inactive',
                                  Updated_On:Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss),
                                  Updated_By: envelope.loggedInUserId
                                }
                                // update member status and asset mapping for the employee
                                await updateStatusAndAssetMapping(organizationDbConnection,employeeId,updateParams,Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss));
                                response.result = {
                                  success: true,
                                  message: `Updated successfully`,
                                  data: resignationId
                                };
                                // function to add system log activities
                                await Common.createSystemLogActivities(systemLogParams);
                              })

                              if (currentDate > resignationDate) {
                                appManagerDbConnection = await Helper.getAppManagerDbConnection(
                                  envelope.orgCode
                                );

                                /** Call function to revoke the access token for the inactive user */
                                await Common.revokeEmployeePotalAccess(organizationDbConnection, appManagerDbConnection, employeeId, envelope.orgCode);

                                // update Firebase_Uid as null in emp_user table
                                // So that employee cannot able to login
                                await organizationDbConnection('emp_user').update({ Firebase_Uid: null }).where('Employee_Id', employeeId);
                                
                                let currentDate=moment().tz(getOrgTimeZone).format('YYYY-MM-DD[T]HH:mm:ss');

                                await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.employeeInfoTimeStampLog,{Employee_Id:employeeId,Action:'InActive',Log_Timestamp:currentDate})
                                
                                /**Update resignation in camu if the camu integration is enabled for the instance */
                                let updateResignationCamu = await Common.callCamuResignationEndpoint(organizationDbConnection, appManagerDbConnection, employeeId, resignationDate, envelope.orgCode);
                                msg = updateResignationCamu ? "" : ". But unable to push the data to camu";
                              }
                              else if(!currentResignationDate.isSame(parsedResignationDate)){
                                let currentDate=moment().tz(getOrgTimeZone).format('YYYY-MM-DD[T]HH:mm:ss');
                                await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.employeeInfoTimeStampLog,{Employee_Id:employeeId,Action:'Active',Log_Timestamp:currentDate})
                              }
                              response.result.message = response.result.message+msg;
                              return response;
                            }else{
                              response.result = {
                                success: true,
                                message: `Updated successfully` + msg,
                                data: resignationId
                              };
                              // function to add system log activities
                              await Common.createSystemLogActivities(systemLogParams);
                              return response
                            }
                          })
                          .catch(function (error) {
                            console.log("Error in .catch block",error);
                            response.error = Common.getError(error, 'ERE0002');
                            return response
                          })
                          .finally(function () {
                            organizationDbConnection.destroy();
                            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                          })
                      } else {
                        organizationDbConnection ? organizationDbConnection.destroy() : "";
                        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                        response.error = Common.getError("", 'ERE0002');
                        return response;
                      }

                  } else {
                    console.log('Resignation record is in ' + ' (' + resignationId  +') '+recordStatus+' status but loggedIn employee is not an admin or employee admin or super admin or service provider admin.');
                    response.error = Common.getError(null, 'ERE0113');
                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                    return response;
                  }
                  }else{
                    console.log('Employee full and final settlement is initiated.');
                    response.error = Common.getError(null, 'ERE0129');
                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                    return response; 
                  }
                }else{
                  console.log('Resignation record does not exist.');
                  response.error = Common.getError(null, 'ERE0114');
                  // destroy database connection
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                  return response;
                }
              } else {
                response.error = timeZone.error;
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                return response
              }
            }
          }
        }else{
          console.log('Empty response from the checkEmployeeAccessRights() in the updateResignationDate() function.',accessRights);
          throw('_DB0102');
        }
      } catch (error) {
        console.log('Error in the updateResignationDate() main catch block.',error);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        if(typeof error === 'object'){
          response.error = Common.getError(error, 'ERE0102');
          response.extendedErrors = error.failedData
        } else{
          response.error = Common.getError(error, 'ERE0102');
        }
        return response
      }
    },

    /**
     * For update a particular resignation details
     * `args` - (envelope, resignationId, resignationDate)
     */
    updateDynamicFormDetail: async (parent, args, root) => {
      console.log('Inside the updateDynamicFormDetail() function.');
      let { envelope, responseId, taskId, formData, formStatus, resignationId, workflowInstanceId } = args;
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection = '';
      try {
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        let timeZone = await Common.getEmployeeTimeZone(
            envelope.loggedInUserId,
            organizationDbConnection
        );
        // if user timeZone available then allow the user to create or update the record
        if (timeZone && !timeZone.error) {
          // Check responseId exist or not if yes update it otherwise insert it
          if (responseId && responseId > 0) {
            return organizationDbConnection('dynamic_form_responses')
                .where('Response_Id', '=', responseId)
                .update({
                  Task_Id: taskId,
                  Response: formData,
                  Status: formStatus,
                  Updated_On: Common.getFormattedDateString(
                      timeZone,
                      Common.constant.YYYYMMDDHHmmss
                  ),
                  Updated_By: envelope.loggedInUserId
                })
                .then(async () => {
                  // Update the resignation status as Applied
                  return(
                    organizationDbConnection('emp_resignation')
                    .update({
                      'Approval_Status' : 'Applied'
                    })
                    .where('Resignation_Id', resignationId)
                    .where('Approval_Status', Common.constant.statusIncomplete) // Update only if the status is Incomplete
                    .then(async (updateRegStatus)=>{
                      console.log('Dynamic form responses updated and resignation status updated successfully.', updateRegStatus);

                      if(updateRegStatus){
                        if(!workflowInstanceId){
                            let workflowInstanceResult = await organizationDbConnection('emp_resignation').select('Workflow_Instance_Id').where('Resignation_Id', resignationId);
                            if(workflowInstanceResult && workflowInstanceResult.length > 0)
                              workflowInstanceId = workflowInstanceResult[0].Workflow_Instance_Id;
                        }
                        
                        if(workflowInstanceId){
                          let resignationData = {
                            resignationId: resignationId,
                            orgCode: envelope.orgCode,
                            updatedBy: envelope.loggedInUserId,
                            updatedOn : Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss),
                            approvalStatus: 'Applied'
                          };
                          let updateInstanceData = await updateWorkflowResignationInstanceData(organizationDbConnection, workflowInstanceId, resignationData);
                        }
                      }
                      
                      // form response and return it to UI.
                      response.result = {
                        success: true,
                        message: `Updated successfully`,
                        data: responseId
                      };
                      // form system log activity function params 
                      await Common.createSystemLogActivities({
                        action: process.env.systemlogEdit,
                        userIp: root.userIp,
                        employeeId: envelope.loggedInUserId,
                        formName: Common.constant.formDynamicFormBuilder,
                        trackingColumn: 'Response Id',
                        organizationDbConnection: organizationDbConnection,
                        uniqueId: responseId
                      });
                      return response
                    })
                  )                  
                })
                .catch(function(error) {
                  response.error = Common.getError(error, 'ERE0005');
                  return response
                })
                .finally(function() {
                  organizationDbConnection.destroy();
                })
          } else {
            return organizationDbConnection('dynamic_form_responses')
                .insert({
                  Task_Id: taskId,
                  Response: formData,
                  Status: formStatus,
                  Added_On: Common.getFormattedDateString(
                      timeZone,
                      Common.constant.YYYYMMDDHHmmss
                  ),
                  Added_By: envelope.loggedInUserId
                })
                .then(async result => {
                  // Update the resignation status as Applied
                  return (
                    organizationDbConnection('emp_resignation')
                    .update({
                      'Approval_Status': 'Applied'
                    })
                    .where('Resignation_Id', resignationId)
                    .where('Approval_Status', Common.constant.statusIncomplete) // Update only if the status is Incomplete
                    .then(async (updateRegStatus) => {
                      console.log('Dynamic form responses inserted and resignation status updated successfully.', updateRegStatus);
                      
                      if(updateRegStatus){
                        if(!workflowInstanceId){
                          let workflowInstanceResult = await organizationDbConnection('emp_resignation').select('Workflow_Instance_Id').where('Resignation_Id', resignationId);
                          if(workflowInstanceResult && workflowInstanceResult.length > 0)
                              workflowInstanceId = workflowInstanceResult[0].Workflow_Instance_Id;
                        }
                        if(workflowInstanceId){
                          let resignationData = {
                            resignationId: resignationId,
                            orgCode: envelope.orgCode,
                            updatedBy: envelope.loggedInUserId,
                            updatedOn : Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss),
                            approvalStatus: 'Applied'
                          };
                          
                          let updateInstanceData = await updateWorkflowResignationInstanceData(organizationDbConnection, workflowInstanceId, resignationData);
                        }
                      }

                      // form response and return it to UI.
                      response.result = {
                        success: true,
                        message: `Created successfully`,
                        data: result[0]
                      };
                      // form system log activity function params 
                      await Common.createSystemLogActivities({
                        action: process.env.systemlogAdd,
                        userIp: root.userIp,
                        employeeId: envelope.loggedInUserId,
                        formName: Common.constant.formDynamicFormBuilder,
                        trackingColumn: 'Response Id',
                        organizationDbConnection: organizationDbConnection,
                        uniqueId: result[0]
                      });
                      return response
                    })
                  )
                })
                .catch(function(error) {
                  console.log('Error in updateDynamicFormDetail() function .catch block',error);
                  response.error = Common.getError(error, 'ERE0006');
                  return response
                })
                .finally(function() {
                  organizationDbConnection.destroy();
                })
          }
        } else {
          response.error = timeZone.error;
          console.log('Error in updateDynamicFormDetail() function else block.');
          return response
        }
      } catch (error) {
        console.log('Error in updateDynamicFormDetail() function main catch block', error);
        response.error = Common.getError(error, 'ERE0105');
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },
    /**
     * For update a particular resignation detail reason
     * `args` - (envelope, resignationId, reasonId, relievingReasonComment)
     */
    updateResignationReason: async (parent, args, root) => {
      let { envelope, resignationId, reasonId, relievingReasonComment } = args;
      
      if(relievingReasonComment){
          relievingReasonComment = relievingReasonComment.replace(/\n/g, ' ').trim();
          args.relievingReasonComment = relievingReasonComment;
      }

      console.log('Inside the update resignation reason function.',args, args.esicReason);
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection;
      let workflowInstanceId = "";
      try {
        /** If the reason id zero then update the default reason id */
        if(!reasonId){
          reasonId = Common.constant.defaultEsicReasonId;
        }
        
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        // to check rights of the employee
        let accessRights = await Common.checkEmployeeAccessRights(
          organizationDbConnection,
          envelope.loggedInUserId,
          null,
          '',
          'ui',
          false,
          envelope.formId ? envelope.formId: Common.constant.formIdResignation
      );
        /** If the employee is having access */
        if (Object.keys(accessRights).length > 0){
          if (accessRights.error) {
            console.log('Error from the checkEmployeeAccessRights() in the updateResignationReason() function.',accessRights);
            response.error = accessRights.error
                ? accessRights.error
                : Common.getError(null, '_DB0102');
            // destroy database connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return response;
          } else {
            accessRights= JSON.parse(accessRights);
            if (accessRights.Role_Update === 0){
              throw('_DB0102');
            }else{
              const validationError = validateUpdateResignationReasonInputs(args);
              if (validationError.length === 0) {
                // get the chosen resignation status
                let recordStatus = await organizationDbConnection('emp_resignation').select('Approval_Status','Workflow_Instance_Id').where('Resignation_Id', resignationId);
                // check record exist or not
                if (recordStatus.length){
                  workflowInstanceId = recordStatus[0].Workflow_Instance_Id;
                  
                  // get the resignation status
                  recordStatus = recordStatus[0].Approval_Status;

                  let invalidRecordStatus = ['withdrawn','canceled','rejected'];
                  /** If the resignation is already withdrawn or canceled or rejected then the reason cannot be updated. */
                  if(!invalidRecordStatus.includes(recordStatus.toLowerCase())){
                    // Assign access into variable
                    let isAllowToEdit;
                    // check if record is Approved Status
                    if (recordStatus === 'Approved'){
                      // if login employee is either admin or employee admin or super admin then resignation record can be edited
                      if(accessRights.Employee_Role.toLowerCase()==='admin'){
                        isAllowToEdit = 1;
                      }else{
                        /** If the login emp is not an admin then check whether they are service provider admin or not. 
                       * if the employee is a service provider admin then allow them to update */
                        let serviceProviderFormAccessRights = await Common.checkEmployeeAccessRights(
                          organizationDbConnection,
                          envelope.loggedInUserId,
                          Common.constant.formServiceProviderAdmin,
                          '',
                          'ui');

                          if (Object.keys(serviceProviderFormAccessRights).length > 0) {
                            if(serviceProviderFormAccessRights.error){
                              console.log('Error when getting service provider accessrights in the updateResignationReason() function.',accessRights);
                              response.error = serviceProviderFormAccessRights.error
                                  ? serviceProviderFormAccessRights.error
                                  : Common.getError(null, '_DB0100');
                              // destroy database connection
                              organizationDbConnection ? organizationDbConnection.destroy() : null;
                              return response;
                            }else{
                              serviceProviderFormAccessRights = JSON.parse(serviceProviderFormAccessRights);
                              if(serviceProviderFormAccessRights.Role_Update){
                                isAllowToEdit = 1;
                              } else {
                                isAllowToEdit = 0;
                              }
                            }
                          }
                      }
                    }else{
                      isAllowToEdit = 1;
                    }
                    // check the isAllowToEdit flag is 1 or 0
                    if(isAllowToEdit === 1){
                      let timeZone = await Common.getEmployeeTimeZone(
                        envelope.loggedInUserId,
                        organizationDbConnection
                      );
                      // If user timeZone available then allow the user to update the reason
                      if (timeZone && !timeZone.error) {
                        // form the relieving reason params
                        let updateRelievingReasonParams = {
                          Reason_Id: reasonId,
                          Relieving_Reason_Comment: relievingReasonComment,
                          Updated_On: Common.getFormattedDateString(
                              timeZone,
                              Common.constant.YYYYMMDDHHmmss
                          ),
                          Updated_By: envelope.loggedInUserId
                        };
                        let resignationData = {
                          resignationId: resignationId,
                          orgCode: envelope.orgCode,
                          updatedBy: envelope.loggedInUserId,
                          updatedOn: Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss),
                          reasonId : reasonId,
                          esicReason : args.esicReason ? args.esicReason : "",
                          relievingReasonComment : relievingReasonComment,
                          approvalStatus: recordStatus
                        };

                        let updateInstanceData = await updateWorkflowResignationInstanceData(organizationDbConnection, workflowInstanceId, resignationData);
                        
                        if(updateInstanceData){
                            // update the resignation record details
                            return organizationDbConnection('emp_resignation')
                            .update(updateRelievingReasonParams)
                            .where('Resignation_Id', resignationId)
                            .then(async (updateResponse) => {
                              if(updateResponse){
                                //Update the emp_job
                                let employeeId = await organizationDbConnection('emp_resignation').select('Employee_Id').where('Resignation_Id', resignationId);
                                employeeId = employeeId[0].Employee_Id;

                                await organizationDbConnection('emp_job').update('Reason_Id', updateRelievingReasonParams.Reason_Id).where('Employee_Id', employeeId)

                                // form system log params
                                let systemLogParams = {
                                  action: process.env.systemlogEdit,
                                  userIp: root.userIp,
                                  employeeId: envelope.loggedInUserId,
                                  formName: Common.constant.formResignation,
                                  trackingColumn: 'Relieving Reason',
                                  organizationDbConnection: organizationDbConnection,
                                  uniqueId: 'Resignation Id:' + resignationId
                                };
                                response.result = {
                                  success: true,
                                  message: `Resignation reason Updated successfully.`,
                                  data: resignationId
                                };
                                // function to add system log activities
                                await Common.createSystemLogActivities(systemLogParams);
                                return response;
                              }else{
                                console.log('Resignation reason is not updated as some of the resignation record(Resignation Id:',resignationId,') is opened or already updated in the same or some other user session. Response for updating the relieving reason. ',updateResponse);
                                response.error = Common.getError('', 'ERE0017');//return record is opened or already updated in the same or some other user session.
                                return response;
                              }
                            })
                            .catch(function (error) {
                              response.error = Common.getError(error, 'ERE0019');
                              return response
                            })
                            .finally(function () {
                              organizationDbConnection.destroy();
                            })
                        } else {
                          organizationDbConnection ? organizationDbConnection.destroy() : "";
                          response.error = Common.getError("", 'ERE0019');
                          return response;
                        }
                      } else {
                        console.log('Error while getting the login employee timezone.',timeZone);
                        response.error = timeZone.error;
                        // destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return response
                      }
                    } else {
                      console.log('Resignation record is in ' + ' (' + resignationId  +') '+recordStatus+' status but loggedIn employee is not an admin or employee admin or super admin.');
                      response.error = Common.getError(null, '_DB0102');
                      // destroy database connection
                      organizationDbConnection ? organizationDbConnection.destroy() : null;
                      return response;
                    }
                  }else{
                    console.log('Relieving reason cannot be updated as the resignation is already ',recordStatus);
                    response.error = Common.getError(null, 'ERE0016');
                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return response;  
                  }
                }else{
                  console.log('Resignation record does not exist.');
                  response.error = Common.getError(null, 'ERE0114');
                  // destroy database connection
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  return response;
                }
              }else{
                console.log('Validation error occured in the updateResignationReason() function.',validationError);
                response.result = {
                  success: false,
                  message: `Unable to update the resignation reason`,
                  validation: validationError
                };
                // destroy database connection
                organizationDbConnection.destroy();
                return response;
              }
            }
          }
        }else{
          console.log('Empty response from the checkEmployeeAccessRights() in the updateResignationReason() function.',accessRights);
          throw('_DB0102');
        }
      } catch (error) {
        console.log('Error in the updateResignationReason() main catch block.',error);
        response.error = Common.getError(error, 'ERE0116');
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return response
      }
    },
    /**
     * withdrawnCancelResignation
     * `args` - (envelope, resignationId, approvalStatus)
     */
    withdrawnCancelResignation: async (parent, args, root) => {
      let { envelope, resignationId, approvalStatus,comment } = args;
      console.log('Inside the withdrawn cancel resignation function.',envelope);
      // return values
      let response = {
        error: null,
        result: null
      };
      let organizationDbConnection;
      try {
        // Get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            envelope.orgCode
        );

        // to check rights of the employee.
        let accessRights = await Common.checkEmployeeAccessRights(
          organizationDbConnection,
          envelope.loggedInUserId,
          null,
          process.env.roleUpdate,
          'Back-end',
          false,
          envelope.formId ? envelope.formId: Common.constant.formIdResignation
      );

        if (accessRights && !accessRights.error) {
          // Validate inputs
          const validationError = validateWithdrawnCancellationInputs(args);
          if(validationError.length === 0){
            // get the chosen record resignation details
            let recordStatus = await organizationDbConnection('emp_resignation').select('Approval_Status','Employee_Id','Resignation_Date', 'Workflow_Instance_Id').where('Resignation_Id', resignationId);
            // check record exist or not
            if (recordStatus.length){
              let resignationEmployeeId = recordStatus[0].Employee_Id;
              let resignationApprovalStatus = recordStatus[0].Approval_Status;//resignation status
              let resignationDate = recordStatus[0].Resignation_Date;
              let workflowInstanceId = recordStatus[0].Workflow_Instance_Id;
              let isEmployeeSettlementInitiated = await employeeSettlementInitiated(organizationDbConnection,resignationEmployeeId);

              if(isEmployeeSettlementInitiated === 0){
                let invalidRecordStatus = ['withdrawn','canceled','rejected'];
                /** If the resignation is not already withdrawn or canceled or rejected then the status can be updated. Otherwise return error message. */
                if(!invalidRecordStatus.includes(resignationApprovalStatus.toLowerCase())){
                  /** If the approval status is withdrawn then only the resignation applying employee id has to withdrawn the resignation */
                  if(approvalStatus.toLowerCase() === 'withdrawn' && envelope.loggedInUserId !== resignationEmployeeId){
                    console.log('Resignation record cannot be withdrawn as the login employee is not the resignation record-employee id.');
                    response.error = Common.getError(null, '_DB0102');
                    // destroy database connection
                    organizationDbConnection.destroy();
                    return response;
                  }else{
                    //Validate Employee Already Exist
                    let getPersonalInfoData = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empPersonalInfo,'Employee_Id', resignationEmployeeId)
                    getPersonalInfoData = getPersonalInfoData[0]

                    //Get the old employeeDetails
                    let commonCheck = {
                      "Emp_First_Name": getPersonalInfoData.Emp_First_Name,
                      "Emp_Last_Name": getPersonalInfoData.Emp_Last_Name,
                      "Nationality": getPersonalInfoData.Nationality,
                      "DOB": getPersonalInfoData.DOB,
                      "Blood_Group": getPersonalInfoData.Blood_Group,
                      "Marital_Status": getPersonalInfoData.Marital_Status,
                    }

                    let alreadyEmployeeExists = await organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
                    .select("*")
                    .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EP.Employee_Id")
                    .where(commonCheck)
                    .andWhere("EJ.Emp_Status", "Active")
                    .whereNot('EJ.Employee_Id', resignationEmployeeId)

                    //Check if the employee active already exists
                    if (alreadyEmployeeExists && alreadyEmployeeExists.length) {
                        //Throw employee already exists error
                        throw 'ESS0126'
                    }
                    //Get the old employeeDetails
                    let oldJobDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empJob,'Employee_Id',resignationEmployeeId)
                    oldJobDetails = oldJobDetails[0]

                    //Get the old bankDetails
                    let oldBankDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empBankDetails,'Employee_Id',resignationEmployeeId)
                    oldBankDetails = oldBankDetails.map((el)=>el.Bank_Account_Number)

                    //Get the contactDetails
                    let oldContactDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.contactDetails,'Employee_Id',resignationEmployeeId)
                    oldContactDetails = oldContactDetails[0]
                    
                    //Get the insuranceDetails
                    let oldInsuranceDetails = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection, ehrTables.empInsurance, 'Employee_Id', resignationEmployeeId)
                    
                    //Validate Already Exists Value 
                    let failedData = await commonLib.func.validateEmployeeAlreadyExistsValues(organizationDbConnection, oldJobDetails.User_Defined_EmpId, oldJobDetails.Emp_Email, oldBankDetails, oldContactDetails, oldJobDetails.External_EmpId, resignationEmployeeId, getPersonalInfoData, oldJobDetails.Pf_PolicyNo, oldInsuranceDetails)
        
                    if(failedData && failedData.length){
                      throw {
                        errorCode: 'ESS0126',
                        failedData: failedData
                      }
                    }

                    let timeZone = await Common.getEmployeeTimeZone(
                      envelope.loggedInUserId,
                      organizationDbConnection
                    );
                    // if user timeZone available then allow the user to create or update the record
                    if (timeZone && !timeZone.error) {
                      let currentDate = Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDD);
                      
                      resignationDate = resignationDate ? (Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDD,resignationDate)) : '';

                      //We are commenting this validation because we are checking the payslip either it was generate for that month or not and it was already done when providing data to UI.
                      // /** If the resignation date is less than the current date then the resignation cannot be withdrawn or canceled */
                      // if (currentDate && resignationDate && resignationDate < currentDate) {
                      //   response.error = Common.getError(null, 'ERE0119');
                      //   // destroy database connection
                      //   organizationDbConnection.destroy();
                      //   return response;
                      // }

                      return (
                      organizationDbConnection
                      .transaction(async function (trx) {
                        // update the resignation record details
                        return(organizationDbConnection('emp_resignation')
                        .update({
                          Withdrawn_Cancellation_Comment: comment,
                          Approval_Status: approvalStatus,
                          Workflow_Status: Common.constant.statusDeleted,
                          Workflow_Instance_Id: null,
                          Updated_On: Common.getFormattedDateString(
                              timeZone,
                              Common.constant.YYYYMMDDHHmmss
                          ),
                          Updated_By: envelope.loggedInUserId
                        })
                        .where('Resignation_Id', resignationId)
                        .where('Lock_Flag', 0)
                        .transacting(trx)
                        .then(async (updateResponse) => {
                          /** If the status is updated */
                          if(updateResponse){

                            let resignationData = {
                              resignationId: resignationId,
                              orgCode: envelope.orgCode,
                              updatedBy: envelope.loggedInUserId,
                              updatedOn : Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss),
                              approvalStatus: approvalStatus,
                              withdrawnCancellationComment: comment
                            };
                            
                            let updateInstanceData = await updateWorkflowResignationInstanceData(organizationDbConnection, workflowInstanceId, resignationData);
                            
                            /** Delete the workflow associated in the `ta_user_task` table */
                            return(
                            organizationDbConnection('ta_user_task')
                            .delete()
                            .where('process_instance_id', workflowInstanceId)
                            .transacting(trx)
                            .then(async (taUserTaskDeleted) => {
                              console.log('Workflow user task details deleted.',taUserTaskDeleted);
                              /** Delete the workflow associated in the `ta_user_task_history` table */
                              return(
                              organizationDbConnection('ta_user_task_history')
                              .delete()
                              .where('process_instance_id', workflowInstanceId)
                              .transacting(trx)
                              .then(async (taUserTaskHistoryDeleted) => {
                                console.log('Workflow user task history details deleted.',taUserTaskHistoryDeleted);
                                return(
                                  organizationDbConnection(ehrTables.empJob)
                                  .update({"Emp_Status":"Active","Emp_InActive_Date":null})
                                  .where('Employee_Id',resignationEmployeeId)
                                  .then(async (data)=>{
                                    console.log('Employee status changed');
                                    // form system log params
                                    let systemLogParams = {
                                      action: process.env.systemlogEdit,
                                      userIp: root.userIp,
                                      employeeId: envelope.loggedInUserId,
                                      formName: Common.constant.formResignation,
                                      trackingColumn: approvalStatus+' Resignation',
                                      organizationDbConnection: organizationDbConnection,
                                      uniqueId: 'Resignation Id:' + resignationId
                                    };

                                    // function to add system log activities
                                    await Common.createSystemLogActivities(systemLogParams);
                                    const syntrumResignationActive = await commonLib.func.integrationAPIIsEnabled(organizationDbConnection, {integrationType: 'Syntrum', direction: 'Push', action: 'Create', entityType: 'Resignation-Withdrawn'});
                                    if(syntrumResignationActive){
                                        const params = { orgCode: envelope.orgCode, partnerId: '-', employeeIds: [resignationId], entityType: 'resignation-withdrawn', action: 'create'};
                                        console.log('Syntrum resignation withdraw push to API request params => ', params);
                                        await commonLib.stepFunctions.triggerStepFunction(process.env.asyncSyntrumAPIStepFunction, 'asyncSyntrumAPIStepFunction', null, params);
                                    }
                                    const paramss = { orgCode: envelope.orgCode, partnerId: '-', employeeIds: [resignationEmployeeId], inActiveRollBack: true, inActiveDate: resignationDate};
                                    await commonLib.stepFunctions.triggerStepFunction(process.env.processAirTicketSummary, 'processAirTicketSummary', null, paramss);
                                    return 'success'; // return success response
                                  })
                                )
                                // // get notification trigger employee details
                                // let resignationEmpName,resignationEmpEmail,resignationUserDefinedEmpId,resignationEmpShortName,empManagerId,resignationEmpNameInChip;
                                // let notificationParams = {};
                                // let responseEmailErrorCode='';

                                // let resignationEmployeeDetails = await Common.getEmailNotificationEmployeeDetails(organizationDbConnection,envelope.orgCode,resignationEmployeeId,1);

                                // // check resignationEmployeeDetails array length
                                // if (resignationEmployeeDetails[0]) {
                                //   // form approver details
                                //   resignationEmpName = resignationEmployeeDetails[0].employee_name;
                                //   // check approverName length if it is greater than 15 then trim 15 characters and three dots
                                //   resignationEmpNameInChip = resignationEmpName.length <= 15 ? resignationEmpName : resignationEmpName.substring(0, 15) + '...';
                                //   resignationEmpEmail = resignationEmployeeDetails[0].emp_email;
                                //   resignationUserDefinedEmpId = resignationEmployeeDetails[0].user_defined_empId;
                                //   resignationEmpShortName = resignationEmployeeDetails[0].emp_first_name.charAt(0) + resignationEmployeeDetails[0].emp_last_name.charAt(0);
                                //   empManagerId = resignationEmployeeDetails[0].manager_id;
                                // }

                                // /** If the employee withdrawn then send email to the reporting manager */
                                // if(approvalStatus.toLowerCase() === 'withdrawn' && resignationEmployeeDetails.length > 0 && empManagerId){
                                //   let employeeReportingManagerDetails = await Common.getEmailNotificationEmployeeDetails(organizationDbConnection,envelope.orgCode,empManagerId,0);

                                //   // check employeeReportingManagerDetails exists or not
                                //   if (employeeReportingManagerDetails.length > 0 && employeeReportingManagerDetails[0].emp_email){
                                //       //Get org logo and hr email address
                                //       let organizationSettingsDetails = await Common.getOrganizationSettingsDetails(organizationDbConnection,envelope.orgCode);
                                //       // for notification params
                                //       notificationParams = {
                                //         "Source": process.env.emailFrom,
                                //         "Template": Common.constant.withdrawnEmailToManager,
                                //         "Destination": {
                                //             "ToAddresses": [employeeReportingManagerDetails[0].emp_email]
                                //         },
                                //         "ReplyToAddresses": [resignationEmpEmail],
                                //         "TemplateData": JSON.stringify({
                                //             orgLogo: organizationSettingsDetails.logoPath ? organizationSettingsDetails.logoPath : '',
                                //             approverName: employeeReportingManagerDetails[0].employee_name,
                                //             withdrawnImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket+ Common.constant.withdrawnImagePath,
                                //             employeeName: resignationEmpName.toUpperCase(),
                                //             employeeNameInChip: resignationEmpNameInChip.toUpperCase(),
                                //             employeeShortName: resignationEmpShortName.toUpperCase(),
                                //             userDefinedEmpId: resignationUserDefinedEmpId,
                                //             reason: comment,
                                //             redirectionUrl: 'https://' + envelope.orgCode + '.' + process.env.domainName + process.env.webAddress + Common.constant.resignationRedirectionPath,
                                //             hrappSupportEmail: organizationSettingsDetails.hrAdminEmailAddress ? organizationSettingsDetails.hrAdminEmailAddress : resignationEmpEmail
                                //         })
                                //       }
                                //   }else {
                                //     console.log('Unable to send email as the reporting manager email id is not configured.');
                                //     responseEmailErrorCode = 'ERE0123';
                                //   } 
                                // }
                                // /** If the reporting manager or employee cancel the resignation then send email to the reporting manager */
                                // else if(approvalStatus.toLowerCase() === 'canceled' && resignationEmployeeDetails.length > 0){
                                //   if(resignationEmpEmail){
                                //     let employeeLoggedInUserDetails = await Common.getEmailNotificationEmployeeDetails(organizationDbConnection,envelope.orgCode,envelope.loggedInUserId,0);

                                //     // check employeeLoggedInUserDetails exists or not
                                //     if (employeeLoggedInUserDetails.length > 0 && employeeLoggedInUserDetails[0].emp_email) {
                                //       //Get org logo and hr email address
                                //       let organizationSettingsDetails = await Common.getOrganizationSettingsDetails(organizationDbConnection,envelope.orgCode);
                                //       // for notification params
                                //       notificationParams = {
                                //         "Source": process.env.emailFrom,
                                //         "Template": Common.constant.cancelEmailToEmployee,
                                //         "Destination": {
                                //             "ToAddresses": [resignationEmpEmail]
                                //         },
                                //         "ReplyToAddresses": [employeeLoggedInUserDetails[0].emp_email],
                                //         "TemplateData": JSON.stringify({
                                //             orgLogo: organizationSettingsDetails.logoPath ? organizationSettingsDetails.logoPath : '',
                                //             employeeName: resignationEmpName,
                                //             cancelImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + Common.constant.cancelImagePath,
                                //             reason: comment,
                                //             redirectionUrl: 'https://' + envelope.orgCode + '.' + process.env.domainName + process.env.webAddress + Common.constant.resignationRedirectionPath,
                                //             hrappSupportEmail: organizationSettingsDetails.hrAdminEmailAddress ? organizationSettingsDetails.hrAdminEmailAddress : employeeLoggedInUserDetails[0].emp_email
                                //         })
                                //       }
                                //     }else {
                                //       console.log('Unable to send email as the logged in employee email id is not configured.');
                                //       responseEmailErrorCode = 'ERE0124';
                                //     }
                                //   }else{
                                //     console.log('Unable to send email as the to email id is not configured.');
                                //     responseEmailErrorCode = 'ERE0123';
                                //   }
                                // }else{
                                //   console.log('Resignation employee details does not exist or manager does not exist for the resignation employee id. So unable to send email.',resignationEmployeeDetails,' and the approval status is ',approvalStatus);
                                // }

                                // /** If the notification params exist */
                                // if(Object.keys(notificationParams).length > 0){
                                //   // call function to send email notification
                                //   let mailResponse = await Common.sendEmailNotifications(notificationParams,process.env.sesRegion);
                                //   console.log('Response from the sendEmailNotifications() function. ',mailResponse);

                                //   // check mailResponse. If any error update the system log
                                //   if (mailResponse === 'false') {
                                //       return 'ERE0122'; //return status updated successfully but unable to send email
                                //   }
                                // }else if(Object.keys(notificationParams).length===0 && responseEmailErrorCode){
                                //   return responseEmailErrorCode; //return status updated successfully but unable to send email as the email id is not configured
                                // }
                              }))
                            }))
                          }else{
                            console.log('Resignation status is not updated as the resignation record(Resignation Id:',resignationId,') is opened or already updated in the same or some other user session. Response for updating the resignation status. ',updateResponse);
                            return 'ERE0018';//return record is opened or already updated in the same or some other user session.
                          }
                        }))
                        })
                        .then((result) => {
                          console.log('Resignation status updated successfully.',result);
                          if(result==='success'){
                            response.result = {
                              success: true,
                              message: `Resignation status updated successfully.`,
                              data: resignationId
                            };
                            return response;//return success response
                          }else{
                            response.error = Common.getError('', result);
                            return response;//return error response
                          }
                        })
                        .catch((withdrawnCancelDbCatcherror) => {
                          console.log('Error while updating the resignation withdrawn or cancelation details in the .catch block.',withdrawnCancelDbCatcherror);
                          response.error = Common.getError(withdrawnCancelDbCatcherror, 'ERE0013');
                          return response;
                        })
                        .finally(() => {
                          organizationDbConnection.destroy();
                        })
                      )
                    } else {
                      console.log('Error while getting the login employee timezone.',timeZone);
                      response.error = timeZone.error;
                      // destroy database connection
                      organizationDbConnection ? organizationDbConnection.destroy() : null;
                      return response;
                    }
                  }
                }else{
                  console.log('The resignation record was already withdrawn or canceled.');
                  response.error = Common.getError(null, 'ERE0118');
                  // destroy database connection
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  return response;  
                }
              }else{
                console.log('Employee full and final settlement is initiated.');
                response.error = Common.getError(null, 'ERE0130');

                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return response;  
              }
            }else{
              console.log('Resignation record does not exist.');
              response.error = Common.getError(null, 'ERE0114');
              // destroy database connection
              organizationDbConnection ? organizationDbConnection.destroy() : null;
              return response;
            }
          }else{
            console.log('Validation error occurred in the withdrawnCancelResignation() function.',validationError);
            response.result = {
              success: false,
              message: `Unable to withdrawn or cancel resignation.`,
              validation: validationError
            };
            // destroy database connection
            organizationDbConnection.destroy();
            return response;
          }
        } else {
          console.log('Error while getting the access rights.',accessRights);
          response.error = accessRights.error
              ? accessRights.error
              : Common.getError(null, '_DB0102');
          // destroy database connection
          organizationDbConnection ? organizationDbConnection.destroy() : null;
          return response;
        }
      } catch (error) {
        console.log('Error in the withdrawnCancelResignation() main catch block.',error);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(typeof error === 'object'){
          response.error = Common.getError(error, 'ERE0120');
          response.extendedErrors = error.failedData
        } else{
          response.error = Common.getError(error, 'ERE0120');
        }
        return response;
      }
    }
  }
};
module.exports.resolvers = resolvers;
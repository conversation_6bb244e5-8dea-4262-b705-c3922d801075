const momentTimeZone = require('moment-timezone');
const ErrorCodes = require('./ErrorCodes');
const AppConstant = require('./AppConstants');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const moment = require('moment-timezone');
const { CommonLib } = require('@cksiva09/hrapp-corelib');
const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');
const AWS = require("aws-sdk");
/*
 * function to check employee access rights
 * params `dbConnection`, `employeeId`, `formName`, `rights`, `source (access from UI or Back-end)`
 */

async function checkEmployeeAccessRights(
  dbConnection,
  employeeId,
  formName,
  rights,
  source = 'Back-end',
  checkIsManagerWithRights = false,
  formId = null
) {
  try {
    let checkAccessRights = await CommonLib.func.checkEmployeeAccessRights(dbConnection, employeeId, formName, rights, source, checkIsManagerWithRights, formId)
    if (checkAccessRights) {
      if(source.toLowerCase() === 'ui'){
        checkAccessRights = JSON.stringify(checkAccessRights)
        return checkAccessRights
      }else{
        return checkAccessRights
      }
    } else {
      return {
        error: getError('', '_DB0001')
      }
    }
  } catch (err) {
    console.log('Error getting the employee rights in checkAccessRights main function', err)
    return {
      error: getError(err, '_DB0002')
    }
  }
}

/*
 * function to get timezone based on employee
 * args `employeeId`, `dbConnection`
 */
function getEmployeeTimeZone(employeeId, dbConnection) {
  // Get current time based on user time zone by using employeeId
  return (
      dbConnection
          .transaction(function(trx) {
            return dbConnection
                .select('TimeZone_Id')
                .from('emp_job')
                .leftJoin('location', 'emp_job.Location_Id', 'location.Location_Id')
                .innerJoin('timezone', 'location.Zone_ID', 'timezone.Zone_ID')
                .where('Employee_Id', '=', employeeId)
                .then(empTimeZone => {
                  return empTimeZone && empTimeZone.length > 0 && empTimeZone[0].TimeZone_Id ? empTimeZone[0].TimeZone_Id : 'Asia/Kolkata';
                })
                .then(trx.commit) /**commit all the transactions */
                .catch(trx.rollback) /**rollback if any error occurs */
          })
          /**return the employee time zone to the calling function*/
          .then(function(result) {
            return result
          })
          /**check and return if any error occured */
          .catch(function(error) {
            return {
              error: getError(error, '_DB0003')
            }
          })
  )
}

/*
 * function to get all designation list for drop downs
 * args `dbConnection`
 */
function getAllDesignation(dbConnection) {
  // Get current time based on user time zone by using employeeId
  return dbConnection
      .select(
          'Designation_Id as designationId',
          'Designation_Name as designationName'
      )
      .from('designation')
      .then(result => {
        return { result }
      })
      .catch(function(error) {
        console.log('Error while call the `getAllDesignation()`', error1)
        return {
          error: getError(error, '_DB0004')
        }
      })
}

/*
 * function to get all department list for drop downs
 * args `dbConnection`
 */
function getAllDepartment(dbConnection) {
  // Get current time based on user time zone by using employeeId
  return dbConnection
      .select(
          'Department_Id as departmentId',
          'Department_Name as departmentName'
      )
      .from('department')
      .then(result => {
        return { result }
      })
      .catch(function(error) {
        console.log('Error while call the `getAllDepartment()`', error1)
        return {
          error: getError(error, '_DB0005')
        }
      })
}

/*
 * function to get all employee type list for drop downs
 * args `dbConnection`
 */
function getAllEmployeeType(dbConnection) {
  // Get current time based on user time zone by using employeeId
  return dbConnection
      .select(
          'EmpType_Id as employeeTypeId',
          'Employee_Type as employeeType'
      )
      .from('employee_type')
      .then(result => {
        return { result }
      })
      .catch(function(error) {
        console.log('Error while call the `getAllEmployeeType()`', error)
        return {
          error: getError(error, '_DB0006')
        }
      })
}

/*
 * function to get all locations list for drop downs
 * args `dbConnection`
 */
function getAllLocation(dbConnection) {
  // Get current time based on user time zone by using employeeId
  return dbConnection
      .select(
          'Location_Id as locationId',
          'Location_Name as locationName'
      )
      .from('location')
      .then(result => {
        return { result }
      })
      .catch(function(error) {
        console.log('Error while call the `getAllLocation()`', error)
        return {
          error: getError(error, '_DB0007')
        }
      })
}

/*
 * function to get defined error values
 * args `actualError - throwed from system`, `definedErrorCode - defined by developer`
 */
function getError(actualError, definedErrorCode) {
  console.log(`${definedErrorCode && ErrorCodes[definedErrorCode] ? `definedErrorCode: ${ErrorCodes[definedErrorCode]}`: ``},
    actualError: ${actualError}`);
  if (actualError && ErrorCodes[actualError]) {
    return ErrorCodes[actualError]
  } else if (
      actualError &&
      actualError.code &&
      (actualError.sqlMessage || ErrorCodes[actualError.code])
  ) {
    if (actualError.sqlMessage) {
      return {
        code: actualError.code,
        message: actualError.sqlMessage
      }
    }
    return ErrorCodes[actualError.code]
  } else if (ErrorCodes[definedErrorCode]) {
    return ErrorCodes[definedErrorCode]
  }
  return ErrorCodes._UH0001
}

/*
 * function to get formatted date string value
 * args `timeZone`, `format`, `date`
 */
function getFormattedDateString(timeZone, format, date = new Date()) {
  let moment =  momentTimeZone.utc(date);
  if (timeZone) {
    return moment.tz(timeZone).format(format);
  }
  return moment.format(format);
}

async function getFileURLPassPort(region, bucketName, fileName, signedUrlExpireSeconds = 3600) {
  try {
    // variable declarations
    let bucket;
    // require aws-sdk to use aws services
    const AWS = require('aws-sdk');

    // Create object for s3 bucket
    const s3 = new AWS.S3({ region: region });

    // get the file name
    bucket = bucketName;

    // Get file name from input
    const fileKey = fileName;

    // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
    await s3.headObject({ Bucket: bucket, Key: fileKey }).promise();

    // Call function getSignedUrl and pass getObject/putObject function to getpresigned url
    return s3.getSignedUrl('getObject', {
      Bucket: bucket,
      Key: fileKey,
      Expires: signedUrlExpireSeconds,
    })
  } catch (error) {
    console.log('Error in getFileURL function catch block', error);
    return '';
  }
}

/**
 * funtion to get employee signed image url
 */
async function getFileURL(orgCode, bucketType, fileName) {
  try {
    // require aws-sdk to use aws services
    const AWS = require('aws-sdk')
    // Create object for s3 bucket
    const s3 = new AWS.S3({ region: process.env.region })
    // get bucket name from environment variable
    bucket = process.env.hrappProfileBucket
    // Get file name from input
    const fileKey =
        process.env.domainName + '_upload/' + orgCode + '_tmp/images/' + fileName

    // Set URL expired time
    const signedUrlExpireSeconds = 60 * 60
    // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
    await s3.headObject({ Bucket: bucket, Key: fileKey }).promise()

    // Call function getSignedUrl and pass getObject/putObject function to getpresigned url
    var url = s3.getSignedUrl('getObject', {
      Bucket: bucket,
      Key: fileKey,
      Expires: signedUrlExpireSeconds
    })
    // return response
    return url
  } catch (error) {
    console.log('Error while retrieving the presigned url', error)
    return ''
  }
}

/**
 * For store user action's
 * @param logDetail
 * @returns {Promise<T|string|string>}
 */
async function createSystemLogActivities(logDetail){
  try{
    // Variable initialization
    let { action, userIp, employeeId, formName, trackingColumn, organizationDbConnection, uniqueId} = logDetail;
    let employeeTimeZone = null;
    // Form tracking message this will be added in table
    let trackingMessage = action + ' - ' + formName + ' - ' + trackingColumn + ' - ' + uniqueId;
    // Check if employeeId exists or not
    if(employeeId){
      // get the employee time zone
      employeeTimeZone = await getEmployeeTimeZone(employeeId, organizationDbConnection);
    }
    // If employeeTimeZone is empty
    employeeTimeZone = employeeTimeZone ? getFormattedDateString(employeeTimeZone, AppConstant.YYYYMMDDHHmmss) : new Date();
    // If userIp is empty
    userIp = userIp ? userIp : '-';
    // Initiate transaction
    const data = {
      Employee_Id : employeeId,
      Ip_Address : userIp,
      Log_Timestamp : employeeTimeZone,
      User_Action : trackingMessage
    };
    return(
        organizationDbConnection
            .transaction(function(trx){
              return(
                  organizationDbConnection('system_log')
                      .insert(data)
                      .transacting(trx)
                      .then(() => {
                        // return response
                        return 'Successfully updated the system logs';
                      })
              )
            })
            /**return the success result to user */
            .then(function (result) {
              return result;
            })
            /**catch organizationDbConnection connectivity errors */
            .catch(function (err) {
              console.log('error occur while updating the system logs', err);
              return 'Could not update the system logs';
            })
    )
  } catch (error) {
    console.log('Error occur while updating the system logs', error);
    return 'Could not update the system logs';
  }
}

/**
 * For get particular field based on params value from tables
 * @param organizationDbConnection
 * @param tableName
 * @param uniqueColName
 * @param uniqueId
 * @param columnQuery
 * @returns {Promise<unknown>}
 */
async function getTrackingFieldName (organizationDbConnection, tableName, uniqueColName, uniqueId, columnQuery) {
  return new Promise(resolve => {
    try{
      // Get tracking field name from corresponding table
      const query = organizationDbConnection(tableName);
      query.select(columnQuery);
      query.where(uniqueColName, '=',uniqueId);
      query.then(colNameArray => {
        // Check if colNameArray is empty or not
        if(colNameArray.length > 0){
          resolve(colNameArray[0])
        } else {
          resolve({})
        }
      })
    } catch (error) {
      console.log('Error while getting the tracking column name', error);
      resolve({})
    }
  })
};

/**
 * For get particular field based on params value from tables
 * @param organizationDbConnection
 * @returns {Promise<unknown>}
 */
async function getEmployeeRelievingReaason (organizationDbConnection) {
  /** Get the employee relieving reason */
  return new Promise((resolve,reject) => {
  organizationDbConnection.select(
  'er.Reason_Id as reasonId','er.ESIC_Reason as esicReasonName')
  .from('esic_reason AS er')
  .where('Form_Id', AppConstant.esicReasonId)
  .orderBy('er.ESIC_Reason')
  .then((fetchRelievingReasonResult) => {
    let relievingReasonResult = [];
    let relievingReasonJson = {};
        
    /** If the relieving reason details exist */
    if(fetchRelievingReasonResult && fetchRelievingReasonResult.length>0){
      for(let relievingReason of fetchRelievingReasonResult){
        relievingReasonJson = {};

        relievingReasonJson.reasonId = relievingReason.reasonId;
        relievingReasonJson.esicReasonName = relievingReason.esicReasonName;

        relievingReasonResult.push(relievingReasonJson);
      }
      resolve(relievingReasonResult);
    }else{
      resolve(relievingReasonResult);
    }
  })
  .catch((fetchRelievingReasonErrorResult) => {
    console.log('Error while getting the employee relieving reason', fetchRelievingReasonErrorResult);
    reject('Error while fetching the relieving reason');
  })
  });
}

/** Get the email notification employee details */
async function getEmailNotificationEmployeeDetails(organizationDbConnection,orgCode,employeeId,isProfilePhotoRequired){
  return (new Promise((resolve,reject) => {
  organizationDbConnection
  .select(
      organizationDbConnection.raw('CONCAT_WS(" ",EMPI.Emp_First_Name, EMPI.Emp_Last_Name) as "employee_name"'),
      'EMPI.Emp_First_Name as emp_first_name','EMPI.Emp_Last_Name as emp_last_name',
      'EMJ.User_Defined_EmpId as user_defined_empId', 'EMJ.Emp_Email as emp_email', 'EMPI.Photo_Path as photo_path','EMJ.Manager_Id as manager_id'
  )
  .from('emp_personal_info as EMPI')
  .innerJoin('emp_job as EMJ', 'EMPI.Employee_Id', 'EMJ.Employee_Id')
  .where('EMPI.Employee_Id', employeeId)
  .where('EMPI.Form_Status', 1)
  .where('EMJ.Emp_Status', 'Active')
  .then((employeeDetails) => {
    // check the length of the employeeDetails array
    if(employeeDetails.length > 0){
      // iterate the array and get the details
      for (let empRecord of employeeDetails){
        /** If the profile photo is required */
        if(isProfilePhotoRequired){
          // get the employee profile picture signed url
          empRecord.photo_path = empRecord.photo_path ? (getFileURL(orgCode,AppConstant.awsImageBucket,empRecord.photo_path)) : '';
        }
      }                  
    }
    // return response
    resolve(employeeDetails);
  })
  .catch((employeeDetailsErrorResult) => {
    console.log('Error in the getEmailNotificationEmployeeDetails() function .catch block', employeeDetailsErrorResult);
    reject('ERE0014');
  })
  })); 
}

//function to send the email notification
async function sendEmailNotifications(notificationParams, region){
  try {
      //  require aws-sdk 
      const AWS = require("aws-sdk");
      // create object for aws SES
      const ses = new AWS.SES({ region });
      //  call function sendTemplatedEmailto send email
      let response = await ses.sendTemplatedEmail(notificationParams).promise();
      // return response
      return true;
  } catch (sendEmailNotificationsError) {
      console.log('error while in sendEmailNotifications() function catch block', sendEmailNotificationsError);
      // return response
      return false;
  }
};

// function to get OrgDetails from table and aws s3 bucket
async function getOrganizationSettingsDetails(organizationDbConnection,orgCode){
  return (
      organizationDbConnection('org_details as ORGD')
      .select('ORGD.Report_LogoPath','ORGD.HR_Admin_Email_Address')
      .where('Org_Code', orgCode)
      .then(async (orgDetails) => {
          // return response
          if(orgDetails.length > 0){
              // call function to form s3 file path for organization logo
              let reportLogoS3Path = orgDetails[0].Report_LogoPath ? await commonLib.func.formS3FilePath(orgDetails[0].Report_LogoPath, orgCode, 'hrapplogo', '', process.env.domainName) : "";
              reportLogoS3Path = reportLogoS3Path ? await commonLib.func.getFileURL(process.env.region, process.env.logoBucket,reportLogoS3Path) : '';
              reportLogoS3Path = reportLogoS3Path ? reportLogoS3Path.split('?'):'';
              // return response to resolver
              return { logoPath: reportLogoS3Path[0], hrAdminEmailAddress: orgDetails[0].HR_Admin_Email_Address};
          }else{
              // return response to resolver
              return { logoPath: "", hrAdminEmailAddress: "" };
          }
      })
      .catch(getOrgLogoError =>{
          console.log('Error in getOrganizationSettingsDetails() function .catch block', getOrgLogoError);
          return { logoPath: "", hrAdminEmailAddress: "" };
      })
  )
}

// function to update member status and asset details of an employee
async function updateStatusAndAssetMapping(organizationDbConnection,employeeId,updateParams,empTimeZone){
  try{
      return (
        organizationDbConnection('em_members')
          .update(updateParams)
          .where('Employee_Id',employeeId)
          .then(async(updateMemberStatus) => {
            let params={
              Employee_Id: null,
              Updated_On: empTimeZone                                            
            }
            return(
              organizationDbConnection('asset_management')
              .update(params)
              .where('Employee_Id',employeeId)
              .then(async(updateAssetDetails) => {
                return 'success';
              })
            ) 
          })
          .catch(error => {
              console.log('Error in updateStatusAndAssetMapping function .catch block.',error);
              return '';
          })
      );
  }
  catch (catchError) {
      console.log('Error in updateStatusAndAssetMapping function main catch block', catchError);
      return '';
  }
};

async function updateWorkflowResignationInstanceData(organizationDbConnection, workflowInstanceId, resignationData, processInstanceTable = "" , instanceData = []){
  try{
      console.log('Inside updateWorkflowResignationInstanceData', workflowInstanceId );
      // get the chosen resignation status
      if(instanceData.length == 0 || processInstanceTable == "") {
        instanceData = await organizationDbConnection('ta_process_instance').select('instance_data').where('process_instance_id', workflowInstanceId);
        processInstanceTable  = 'ta_process_instance'; 
        
        if(instanceData.length == 0){
            instanceData = await organizationDbConnection('ta_process_instance_history').select('instance_data').where('process_instance_id', workflowInstanceId);
            processInstanceTable= 'ta_process_instance_history';
        } 
      }
      console.log(processInstanceTable, instanceData);
      if(instanceData.length > 0 && instanceData[0].instance_data){
        instanceData = JSON.parse(instanceData[0].instance_data);
          if(instanceData.employeeId && instanceData.formName && instanceData.formId) {
            let updateInstanceData =  {
              resignationId: resignationData.resignationId,
              employeeId: instanceData.employeeId,
              orgCode: resignationData.orgCode,
              isSelfApply: instanceData.isSelfApply,
              formName: instanceData.formName,
              formId: instanceData.formId,
              fileName: resignationData.fileName ? resignationData.fileName : (instanceData.fileName ? instanceData.fileName : ""),
              appliedDate: resignationData.appliedDate ? resignationData.appliedDate : (instanceData.appliedDate ? instanceData.appliedDate : ""),
              exitDate: resignationData.exitDate ? resignationData.exitDate : (instanceData.exitDate ? instanceData.exitDate : "") ,
              esicReason: resignationData.esicReason ? resignationData.esicReason : (instanceData.esicReason ? instanceData.esicReason : ""),
              reasonId : resignationData.reasonId ? parseInt(resignationData.reasonId) : (instanceData.reasonId ? instanceData.reasonId : 0),
              addedOn: instanceData.addedOn ? instanceData.addedOn : "",
              initiatorId: instanceData.initiatorId ? instanceData.initiatorId : 0,
              updatedOn: resignationData.updatedOn,
              updatedBy: resignationData.updatedBy,
              approvedOn: resignationData.approvedOn ? resignationData.approvedOn : (instanceData.approvedOn ? instanceData.approvedOn : ""),
              approvedBy: resignationData.approvedBy ? resignationData.approvedBy : (instanceData.approvedBy ? instanceData.approvedBy : 0),
              approvalStatus : resignationData.approvalStatus ? resignationData.approvalStatus : (instanceData.approvalStatus ? instanceData.approvalStatus : ""),
              relievingReasonComment : resignationData.relievingReasonComment ? resignationData.relievingReasonComment : (instanceData.relievingReasonComment ? instanceData.relievingReasonComment : ""),
              withdrawnCancellationComment : resignationData.withdrawnCancellationComment ? resignationData.withdrawnCancellationComment : (instanceData.withdrawnCancellationComment ? instanceData.withdrawnCancellationComment : "")
            }
            console.log("Instance Data: ", updateInstanceData);
            return (
              organizationDbConnection(processInstanceTable)
                .update('instance_data', JSON.stringify(updateInstanceData))
                .where('process_instance_id', workflowInstanceId)
                .then(updateInstanceDataRes => {
                  console.log("updateInstanceDataResponse: ", updateInstanceDataRes);
                  return 1;
                }).catch(function(error) {
                  console.log("Error while updating the instance data json", error);
                  return 0;
                })
            )
          } else {
            return 0;
          }
      }

  } catch(err){
    console.log("Error in updateWorkflowResignationInstanceData catch block, ", err);
    return 0;
  }
}

async function updateWorkflowLeaveInstanceData(organizationDbConnection, workflowInstanceId, leaveData, instanceData){
  try{
      
        if(instanceData.employeeId && instanceData.formName && instanceData.formId) {
          let updateInstanceData =  {
            "reason": instanceData.reason ? instanceData.reason : "",
            "endDate": instanceData.endDate ? instanceData.endDate : "",
            "alternatePerson": instanceData.alternatePerson ? instanceData.alternatePerson : 0,
            "duration": instanceData.duration ? instanceData.duration : "",
            "leavePeriod": instanceData.leavePeriod ? instanceData.leavePeriod : "",
            "reasonId": instanceData.reasonId ? instanceData.reasonId : 0,
            "orgCode": instanceData.orgCode ? instanceData.orgCode : "",
            "formName": instanceData.formName ? instanceData.formName : "Leaves",
            "formId": instanceData.formId ? instanceData.formId : 31,
            "approvalStatus": leaveData.approvalStatus ? leaveData.approvalStatus : (instanceData.approvalStatus ? instanceData.approvalStatus : ""),
            "hours": instanceData.hours ? instanceData.hours : "",
            "updatedBy": instanceData.updatedBy ? instanceData.updatedBy : 0,
            "addedBy": instanceData.addedBy ? instanceData.addedBy : 0,
            "employeeId": instanceData.employeeId ? instanceData.employeeId : 0,
            "initiatorId": instanceData.initiatorId ? instanceData.initiatorId : 0,
            "esicReason": instanceData.esicReason ? instanceData.esicReason : "",
            "updatedOn": instanceData.updatedOn ? instanceData.updatedOn : "",
            "addedOn": instanceData.addedOn ? instanceData.addedOn : "",
            "isSelfApply": instanceData.isSelfApply ? instanceData.isSelfApply : 0,
            "totalDays": instanceData.totalDays ? instanceData.totalDays : "",
            "documentList": instanceData.documentList ? instanceData.documentList : {},
            "lateAttendance": instanceData.lateAttendance ? instanceData.lateAttendance : 0,
            "lateAttendanceHours": instanceData.lateAttendanceHours ? instanceData.lateAttendanceHours : "", 
            "comment": instanceData.comment ? instanceData.comment : "",
            "leaveId": instanceData.leaveId ? instanceData.leaveId : 0,
            "leaveTypeId": instanceData.leaveTypeId ? instanceData.leaveTypeId : 0,
            "startDate": instanceData.startDate ? instanceData.startDate : "",
            "contactNo": instanceData.contactNo ? instanceData.contactNo : "",
            "approvedOn": leaveData.Approved_On ? leaveData.Approved_On : (instanceData.approvedOn ? instanceData.approvedOn : ""),
            "approvedBy": leaveData.Approved_By ? leaveData.Approved_By : (instanceData.approvedBy ? instanceData.approvedBy : 0)
          };
          return (
            organizationDbConnection('ta_process_instance_history')
              .update('instance_data', JSON.stringify(updateInstanceData))
              .where('process_instance_id', workflowInstanceId)
              .then(updateInstanceDataRes => {
                return 1;
              }).catch(function(error) {
                console.log("Error while updating the instance data json", error);
                return 0;
              })
          )
        } else {
          return 0;
        }

  } catch(err){
    console.log("Error in updateWorkflowLeaveInstanceData catch block, ", err);
    return 0;
  }
}

async function updatePreApprovalRequestInstanceData(organizationDbConnection, workflowInstanceId, instanceData){
  try{
        return (
          organizationDbConnection('ta_process_instance_history')
            .update('instance_data', JSON.stringify(instanceData))
            .where('process_instance_id', workflowInstanceId)
            .then(updateInstanceDataRes => {
              return 1;
            }).catch(function(error) {
              console.log("Error while updating the instance data json", error);
              return 0;
            })
        )

  } catch(err){
    console.log("Error in updatePreApprovalRequestInstanceData catch block, ", err);
    return 0;
  }
}


async function revokeEmployeePotalAccess(organizationDbConnection, appManagerDbConnection, employeeId, orgCode){
  try{
    console.log('Inside revokeEmployeePotalAccess function');
    return ( organizationDbConnection
          .select('Firebase_Uid')
          .from('emp_user')
          .where('Employee_Id', employeeId)
          .then(async(result) => {
              if (result.length > 0) {
                  let firebaseUserId = result[0].Firebase_Uid;
                  if (firebaseUserId) {
                      await appManagerDbConnection
                      .select('Partner_Integration')
                      .from('hrapp_registeruser')
                      .where('Org_Code', orgCode)
                      .then(async(partnerIntegrationResult) => {
                          if(partnerIntegrationResult.length > 0){
                              let partnerId = (partnerIntegrationResult[0].Partner_Integration) ? partnerIntegrationResult[0].Partner_Integration : '-';
                              await commonLib.firebase.revokeRefreshToken(partnerId,process.env.region,process.env.secretName,firebaseUserId);
                              return "success";
                          }
                      })
                      .catch(function(partnerIntegrationError) {
                          console.log('Error while getting the partner integration for the org code.', partnerIntegrationError);
                          return "";
                      });
                  }
              }
          })
          .catch(function(error) {
              console.log('Error while getting the firebase user id for the employee or revoking the refresh token', error);
              return "";
          })
    )
  } catch(err){
    console.log("Error in revokeEmployeePotalAccess catch block, ", err);
    return "";
  }
}

async function callCamuResignationEndpoint(organizationDbConnection, appManagerDbConnection, employeeId, exitDate, orgCode){
  try{
    console.log('Inside callCamuResignationEndpoint function');
    //Get Partner_Integration and Client_Access_Token 
    let camuIntegrationDetails=await commonLib.employees.getCamuClientToken(appManagerDbConnection,orgCode,organizationDbConnection,[employeeId]);
    
    if(camuIntegrationDetails && camuIntegrationDetails.Partner_Integration.toLowerCase() === 'camu'){
      let camuToken,camuBaseUrl;
      if(camuIntegrationDetails.fieldForceEnabled){
        camuToken=camuIntegrationDetails['empCamuDetails'][employeeId][0]['Client_Access_Token'];
        camuBaseUrl=camuIntegrationDetails['empCamuDetails'][employeeId][0]['Camu_Base_Url'];
      }else{
        camuToken=camuIntegrationDetails['empCamuDetails']['Client_Access_Token'];
        camuBaseUrl=camuIntegrationDetails['empCamuDetails']['Camu_Base_Url'];
      }
           console.log("camuToken",camuToken,"camuBaseUrl",camuBaseUrl)
      if(camuBaseUrl){

        let employeeCamuData= await commonLib.employees.getCamuResignationEmployeeInfo(organizationDbConnection,employeeId);
        
        employeeCamuData = employeeCamuData[0];
        employeeCamuData.exitDate = exitDate;
        let camuExitStaffEndPoint=camuBaseUrl+process.env.camuExitStaffEndPoint;
        console.log("camuExitStaffEndPoint",camuExitStaffEndPoint)
        let exitCallResponse=await commonLib.employees.callCamuExitStaffApi(employeeCamuData,camuExitStaffEndPoint,camuToken);
        let insertParams = {"Employee_Id":employeeId,
                          "Data_Processing_Date":moment.utc().format("YYYY-MM-DD")};
        if(exitCallResponse)
        {
          insertParams.Status = 'Success';
        }
        else{
            insertParams.Status = 'Failed';
        }
        /**If the record not availble for the employee then it will add otherwise it will throw duplicate employee id error */
        let insertIntoCamuResignationManager = await commonLib.func.insertIntoTable(organizationDbConnection,"camu_employee_resignation_manager",insertParams);
        
        if(!insertIntoCamuResignationManager){
          insertIntoCamuResignationManager = await organizationDbConnection('camu_employee_resignation_manager').update({ Status: insertParams.Status }).where('Employee_Id', employeeId).then(res => {return true});
        }
        
        return exitCallResponse;

      } else {
        console.log('Camu base url is not configured.');
        return false;
      }
    } else {
      /** Return true when the camu integration is not enabled */
      return true;
    }

  } catch(err){
    console.log("Error in callCamuResignationEndpoint catch block", err);
    return false;
  }
}

//Function to check employee full and final settlement is initiated or not
async function employeeSettlementInitiated(organizationDbConnection,employeeId){
  return (
      organizationDbConnection('employee_full_and_final_settlement as FF')
      .pluck('FF.Employee_Id')
      .where('FF.Employee_Id', employeeId)
      .then(async (result) => {
          // return response
          if(result && result.length > 0){
            return 1;
          }else{
            return 0;
          }
      })
      .catch(error =>{
          console.log('Error in employeeSettlementInitiated() function .catch block', getOrgLogoError);
          throw error;
      })
  )
}
const retrieveEmployeeTravel = async (organizationDbConnection, requestId) => {
  try {
    // Retrieve main travel request details along with employee information
    const travelRequest = await organizationDbConnection(`emp_travel_request as T`)
      .select(
        'T.Request_Id',
        'T.Employee_Id',
        'T.Trip_Name',
        'T.Travel_Type',
        'T.Business_Purpose',
        'T.Trip_Type',
        'T.Budget_Amount',
        'T.Destination_Country',
        'T.Visa_Required',
        'T.Process_Instance_Id',
        organizationDbConnection.raw('IFNULL(T.Destination_Country, "") as Destination_Country'),
        organizationDbConnection.raw('IFNULL(T.Visa_Required, false) as Visa_Required'),
        'T.Status as status',
        'T.Added_On as addedOn',
        'T.Updated_On as updatedOn',
        'T.Meal_Preference as mealPreference',
        'T.Seat_Preference as seatPreference',
        'T.Travel_Start_Date as travelStartDate',
        'T.Travel_End_Date as travelEndDate',
        organizationDbConnection.raw(`
          CASE 
            WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id 
            ELSE EJ.User_Defined_EmpId 
          END as userDefinedEmpId
        `),
        organizationDbConnection.raw(`
          CONCAT_WS(" ", EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as employeeName
        `),
        organizationDbConnection.raw(`
          CONCAT_WS(" ", EP_U.Emp_First_Name, EP_U.Emp_Middle_Name, EP_U.Emp_Last_Name) as updatedByName
        `),
        organizationDbConnection.raw(`
          CONCAT_WS(" ", EP_A.Emp_First_Name, EP_A.Emp_Middle_Name, EP_A.Emp_Last_Name) as addedByName
        `)
      )
      .leftJoin(`${ehrTables.empPersonalInfo} as EP`, 'EP.Employee_Id', 'T.Employee_Id')
      .leftJoin(`${ehrTables.empJob} as EJ`, 'EJ.Employee_Id', 'T.Employee_Id')
      .leftJoin(`${ehrTables.empPersonalInfo} as EP_U`, 'EP_U.Employee_Id', 'T.Updated_By')
      .leftJoin(`${ehrTables.empPersonalInfo} as EP_A`, 'EP_A.Employee_Id', 'T.Added_By')
      .where('T.Request_Id', requestId)
      .first();

    if (!travelRequest) {
      throw new Error('Travel request not found');
    }

    // Create promise queries for each travel detail type
    const flightDetailsPromise = organizationDbConnection('emp_travel_flight_details')
      .select(
        'Depart_From as departFrom',
        'Arrive_At as arriveAt',
        'Departure_Date as departureDate',
        'Airline_Preference as airlinePreference',
        'Return_Date as returnDate',
        'Departure_Time_Preference as departureTimePreference',
        'Arrival_Time_Preference as arrivalTimePreference',
        'Description as description',
        'Self_Booking as selfBooking'
      )
      .where('Request_Id', requestId);

    const hotelStaysPromise = organizationDbConnection('emp_travel_hotel_stays')
      .select(
        'Location as location',
        'Check_In_Datetime as checkInDatetime',
        'Check_Out_Datetime as checkOutDatetime',
        'Hotel_Preference as hotelPreference',
        'Description as description',
        'Self_Booking as selfBooking'
      )
      .where('Request_Id', requestId);

    const trainDetailsPromise = organizationDbConnection('emp_travel_train_details')
      .select(
        'Depart_From as departFrom',
        'Arrive_At as arriveAt',
        'Departure_Date as departureDate',
        'Time_Preference as timePreference',
        'Description as description',
        'Self_Booking as selfBooking'
      )
      .where('Request_Id', requestId);

    const busDetailsPromise = organizationDbConnection('emp_travel_bus_details')
      .select(
        'Depart_From as departFrom',
        'Arrive_At as arriveAt',
        'Departure_Date as departureDate',
        'Time_Preference as timePreference',
        'Description as description',
        'Self_Booking as selfBooking'
      )
      .where('Request_Id', requestId);

    const carDetailsPromise = organizationDbConnection('emp_travel_car_details')
      .select(
        'Depart_From as departFrom',
        'Arrive_At as arriveAt',
        'Pick_Up_Date_Time as pickUpDateTime',
        'Drop_Date_Time as dropDateTime',
        'Car_Type as carType',
        'Driver_Needed as driverNeeded',
        'Description as description',
        'Self_Booking as selfBooking'
      )
      .where('Request_Id', requestId);

    // Execute all detail queries concurrently
    const [flightDetails, hotelStays, trainDetails, busDetails, carDetails] = await Promise.all([
      flightDetailsPromise,
      hotelStaysPromise,
      trainDetailsPromise,
      busDetailsPromise,
      carDetailsPromise
    ]);

    // Combine into the expected response structure
    const result = {
      tripId: travelRequest.Request_Id,
      userDefinedEmpId: travelRequest.userDefinedEmpId,
      employeeName: travelRequest.employeeName,
      employeeId: travelRequest.Employee_Id,
      tripName: travelRequest.Trip_Name,
      travelType: travelRequest.Travel_Type,
      tripType: travelRequest.Trip_Type,
      businessPurpose: travelRequest.Business_Purpose,
      budgetAmount: travelRequest.Budget_Amount,
      mealPreference: travelRequest.mealPreference,
      processInstanceId: travelRequest.Process_Instance_Id,
      seatPreference: travelRequest.seatPreference,
      destinationCountry: travelRequest.Destination_Country,
      visaRequired: travelRequest.Visa_Required,
      status: travelRequest.status,
      flightDetails,
      hotelStays,
      trainDetails,
      busDetails,
      carDetails,
      updatedByName: travelRequest.updatedByName,
      updatedOn: travelRequest.updatedOn,
      addedOn: travelRequest.addedOn,
      addedByName: travelRequest.addedByName
    };

    return result;
  } catch (error) {
    console.error('Error in retrieveEmployeeTravel:', error);
    throw error;
  }
};
async function formTravelDetailsTable(detailsArray, tableHeaders, tableName) {
  try {
      if (!detailsArray || detailsArray.length === 0) {
          return '';
      }

      // Generate table headers
      let trHead = `
          <tr align="left" style="border:2px solid #e2e7ee">
              ${tableHeaders.map(header => `<th style="color: #595C68; padding: 8px; font-size: clamp(11px, 3vw, 12px); word-break: normal;">${header}</th>`).join('')}
          </tr>`;

      // Generate table rows from detailsArray
      let trRows = '';
      detailsArray.forEach(item => {
          let cells = '';
          
          // Create table cells based on the type of detail
          if (tableName === 'Flight Details') {
              cells = `
                  <td data-label="Trip Type">${travelDetails.tripType || '-'}</td>
                  <td data-label="Depart From">${item.departFrom || '-'}</td>
                  <td data-label="Arrive At">${item.arriveAt || '-'}</td>
                  <td data-label="Departure Date">${item.departureDate || '-'}</td>
                  <td data-label="Time Preference">${item.departureTimePreference || '-'}</td>
                  <td data-label="Airline Preference">${item.airlinePreference || '-'}</td>
                  <td data-label="Self Booking">${item.selfBooking ? 'Yes' : 'No'}</td>`;
          } else if (tableName === 'Hotel Details') {
              cells = `
                  <td data-label="Location">${item.location || '-'}</td>
                  <td data-label="Check-in">${item.checkInDatetime || '-'}</td>
                  <td data-label="Check-out">${item.checkOutDatetime || '-'}</td>
                  <td data-label="Hotel Preference">${item.hotelPreference || '-'}</td>
                  <td data-label="Self Booking">${item.selfBooking ? 'Yes' : 'No'}</td>`;
          } else if (tableName === 'Train Details') {
              cells = `
                  <td data-label="Depart From">${item.departFrom || '-'}</td>
                  <td data-label="Arrive At">${item.arriveAt || '-'}</td>
                  <td data-label="Departure Date">${item.departureDate || '-'}</td>
                  <td data-label="Time Preference">${item.timePreference || '-'}</td>
                  <td data-label="Self Booking">${item.selfBooking ? 'Yes' : 'No'}</td>`;
          } else if (tableName === 'Bus Details') {
              cells = `
                  <td data-label="Depart From">${item.departFrom || '-'}</td>
                  <td data-label="Arrive At">${item.arriveAt || '-'}</td>
                  <td data-label="Departure Date">${item.departureDate || '-'}</td>
                  <td data-label="Time Preference">${item.timePreference || '-'}</td>
                  <td data-label="Self Booking">${item.selfBooking ? 'Yes' : 'No'}</td>`;
          } else if (tableName === 'Car Details') {
              cells = `
                  <td data-label="Depart From">${item.departFrom || '-'}</td>
                  <td data-label="Arrive At">${item.arriveAt || '-'}</td>
                  <td data-label="Pick-up Time">${item.pickUpDateTime || '-'}</td>
                  <td data-label="Drop Time">${item.dropDateTime || '-'}</td>
                  <td data-label="Car Type">${item.carType || '-'}</td>
                  <td data-label="Driver Needed">${item.driverNeeded ? 'Yes' : 'No'}</td>
                  <td data-label="Self Booking">${item.selfBooking ? 'Yes' : 'No'}</td>`;
          }

          trRows += `
              <tr style="border:1px solid #e2e7ee">
                  ${cells}
              </tr>`;
      });

      // Add responsive styles with media query
      const responsiveStyle = `
          <style>
              @media screen and (max-width: 768px) {
                  .travel-table {
                      border: 0;
                  }
                  .travel-table thead {
                      display: none;
                  }
                  .travel-table tr {
                      margin-bottom: 10px;
                      display: block;
                      border-bottom: 2px solid #e2e7ee;
                  }
                  .travel-table td {
                      display: block;
                      text-align: right;
                      font-size: 12px;
                      padding-left: 45% !important;
                      position: relative;
                      border-bottom: 1px solid #eee;
                  }
                  .travel-table td:before {
                      content: attr(data-label);
                      position: absolute;
                      left: 10px;
                      font-weight: bold;
                      text-align: left;
                  }
              }
          </style>`;

      // Form final html section with section title and table
      let sectionHtml = `
          ${responsiveStyle}
          <div style="margin-top: 5px; padding: 15px;">
              <h3 style="color: #333; font-size: clamp(14px, 4vw, 16px);">${tableName}</h3>
              <div style="overflow-x: auto;">
                  <table cellpadding="10" cellspacing="0" class="travel-table" style="border-collapse:collapse; color:#260029; font-size:clamp(11px, 3vw, 12px); width:100%">
                      <thead>
                          ${trHead}
                      </thead>
                      <tbody>
                          ${trRows}
                      </tbody>
                  </table>
              </div>
          </div>`;

      return sectionHtml;
  } catch (e) {
      console.log(`Error in the formTravelDetailsTable() function for ${tableName}.`, e);
      return "";
  }
}
async function getCustomEmailAttachment(organizationDbConnection, orgCode, bucketName, region, Employee_Id, emailContent) {
  try {
    // Fetch the attachments from the database
    const customEmailAttachmentsData = await organizationDbConnection('emp_passport')
      .where('Employee_Id', Employee_Id)
      .select('File_Name');

    // Parse Attachments JSON safely
    const customEmailAttachments = customEmailAttachmentsData.length > 0 
  ? customEmailAttachmentsData.map(row => row.File_Name) // Extract File_Name values
  : [];
      

    // If no attachments are found, return the original email content
    if (!customEmailAttachments.length) {
      return '';
    }

    // Generate file links
    const attachmentLinks = await Promise.all(
      customEmailAttachments.map(async (fileName) => {
        if (!fileName) return null; // Handle null or empty filenames
        const formedFileName = `${process.env.domainName}/${orgCode}/Employees Document Upload/${fileName}`;
        const path = await getFileURLPassPort(region, bucketName, formedFileName, 9000);
        return path ? `<li><a href="${path}" target="_blank">${fileName}</a></li>` : null;
      })
    );

    // Filter out any null values
    const validLinks = attachmentLinks.filter(Boolean);

    // If no valid links, return the original email content
    if (!validLinks.length) {
      return '';
    }

    // Append attachments as a list at the bottom of the existing email content
    return `<br/><strong>Passport Attachments:</strong><ul>${validLinks.join('')}</ul>`;
  } catch (error) {
    console.error('Error fetching custom email attachments:', error);
    return ''; // Return original content in case of an error
  }
}
async function travelDetailsTemplate(travelDetails, orgCode, organizationDbConnection, context, Employee_Id) {
  try {
      // Retrieve organization details
      let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection,1);
      let organizationAddress = [], companyName = "";
      companyName = orgDetails.orgName;
      let replyTo=orgDetails.hrAdminEmailAddress;
      organizationAddress = await commonLib.func.getOrgAddress(organizationDbConnection, Employee_Id);
      
      // Extract address components
      let street1 = organizationAddress.length > 0 ? organizationAddress[0].Street1 : '';
      let street2 = organizationAddress.length > 0 ? organizationAddress[0].Street2 : '';
      let pinCode = organizationAddress.length > 0 ? organizationAddress[0].Pincode : '';
      let cityName = organizationAddress.length > 0 ? organizationAddress[0].City_Name : '';
      let stateName = organizationAddress.length > 0 ? organizationAddress[0].State_Name : '';
      let countryName = organizationAddress.length > 0 ? organizationAddress[0].Country_Name : '';

      // Get company logo
      let reportLogoS3Path = null;
      let result = await getSenderNameAndLogo(organizationDbConnection);
      let Add_Logo = result?.Add_Logo;
      if (Add_Logo?.toLowerCase() === 'no') {
          // Handle entomo case
      } else {
          reportLogoS3Path = orgDetails.Report_LogoPath ? process.env.domainName + '_upload/' + orgCode + '_tmp/logos/' + orgDetails.Report_LogoPath : '';
      }


      // Define headers for each type of travel detail
      const flightHeaders = ['Trip Type','Departure City', 'Arrival City', 'Departure Date', 'Time Preference', 'Airline Preference', 'Self Booking'];
      const hotelHeaders = ['Location', 'Check-in Date/Time', 'Check-out Date/Time', 'Hotel Preference','Self Booking'];
      const trainHeaders = ['Departure City', 'Arrival City', 'Departure Date', 'Time Preference','Self Booking'];
      const busHeaders = ['Departure City', 'Arrival City', 'Departure Date', 'Time Preference','Self Booking'];
      const carHeaders = ['Pickup Location', 'Drop Location', 'Pickup Date/Time', 'Drop Date/Time', 'Car Type', 'Driver Needed','Self Booking'];

      // Generate tables for each travel component
      const flightTable = travelDetails.flightDetails.length > 0 ? await formTravelDetailsTable(travelDetails.flightDetails, flightHeaders, 'Flight Details') : '';
      const hotelTable = travelDetails.hotelStays.length > 0 ? await formTravelDetailsTable(travelDetails.hotelStays, hotelHeaders, 'Hotel Details') : '';
      const trainTable = travelDetails.trainDetails.length > 0 ? await formTravelDetailsTable(travelDetails.trainDetails, trainHeaders, 'Train Details') : '';
      const busTable = travelDetails.busDetails.length > 0 ? await formTravelDetailsTable(travelDetails.busDetails, busHeaders, 'Bus Details') : '';
      const carTable = travelDetails.carDetails.length > 0 ? await formTravelDetailsTable(travelDetails.carDetails, carHeaders, 'Car Details') : '';

      // Format trip status with color coding
      let statusColor = '#28a745'; // Default green for approved status
      if (travelDetails.status.includes('Cancel') || travelDetails.status.includes('Rejected')) {
          statusColor = '#dc3545'; // Red for cancelled or rejected
      } else if (travelDetails.status.includes('Pending')) {
          statusColor = '#ffc107'; // Yellow for pending
      }
      
      // Add company logo to header if available
      let logoHtml = '';
      if (reportLogoS3Path) {
          logoHtml = `<div style="text-align: center; margin-bottom: 20px;">
              <img src="${reportLogoS3Path}" alt="${companyName}" style="max-height: 80px; max-width: 200px;">
          </div>`;
      }

      // Create the main travel details summary section
      const travelSummary = `
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; max-width: 100%;">
          <h2 style="color: #333; margin-bottom: 15px; font-size: clamp(18px, 5vw, 24px);">Travel Request Summary</h2>
          <div style="overflow-x: auto;">
              <table cellpadding="5" cellspacing="0" style="width: 100%; font-size: clamp(12px, 3vw, 14px); border-collapse: collapse;">
                  <tr>
                      <td style="width: 30%; font-weight: bold; padding: 8px 5px;">Employee ID:</td>
                      <td style="padding: 8px 5px;">${travelDetails.userDefinedEmpId || '-'}</td>
                  </tr>
                  <tr>
                      <td style="font-weight: bold; padding: 8px 5px;">Employee Name:</td>
                      <td style="padding: 8px 5px;">${travelDetails.employeeName || '-'}</td>
                  </tr>
                  <tr>
                      <td style="font-weight: bold; padding: 8px 5px;">Trip Name:</td>
                      <td style="padding: 8px 5px;">${travelDetails.tripName || '-'}</td>
                  </tr>
                  <tr>
                      <td style="font-weight: bold; padding: 8px 5px;">Destination:</td>
                      <td style="padding: 8px 5px;">${travelDetails.destinationCountry || '-'}</td>
                  </tr>
                  <tr>
                      <td style="font-weight: bold; padding: 8px 5px;">Visa Required:</td>
                      <td style="padding: 8px 5px;">${travelDetails.visaRequired ? 'Yes' : 'No'}</td>
                  </tr>
                  <tr>
                      <td style="font-weight: bold; padding: 8px 5px;">Meal Preference:</td>
                      <td style="padding: 8px 5px;">${travelDetails.mealPreference || '-'}</td>
                  </tr>
                  <tr>
                      <td style="font-weight: bold; padding: 8px 5px;">Status:</td>
                      <td style="padding: 8px 5px;"><span style="color: ${statusColor}; font-weight: bold;">${travelDetails.status || '-'}</span></td>
                  </tr>
                  <tr>
                      <td style="font-weight: bold; padding: 8px 5px;">Seat Preference:</td>
                      <td style="padding: 8px 5px;">${travelDetails.seatPreference || '-'}</td>
                  </tr>
              </table>
          </div>
      </div>
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; max-width: 100%;">
          <h3 style="color: #333; margin-bottom: 10px; font-size: clamp(16px, 4vw, 20px);">Business Purpose</h3>
          <p style="margin: 0; padding: 0; font-size: clamp(12px, 3vw, 14px);">${travelDetails.businessPurpose || '-'}</p>
      </div>`;

      // Create footer with company information
      const footerHtml = `
      <div style="margin: 10px auto">
          <div class="block-grid" style="Margin: 0 auto; min-width: 320px; max-width: 650px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;">
              <div style="border-collapse: collapse;display: table;width: 100%;">
                  <div class="col num12" style="min-width: 320px; max-width: 650px; display: table-cell; vertical-align: top; width: 650px;">
                      <div style="width:100% !important;">
                          <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-bottom:15px; padding-right: 20px; padding-left: 20px;">
                              <div style="color:#6B6B6B;font-family:'Open Sans', Helvetica, Arial, sans-serif;line-height:1.2;padding-right:10px;padding-bottom:5px;padding-left:10px;">
                                  <div style="font-family: 'Open Sans', Helvetica, Arial, sans-serif; font-size: 12px; line-height: 1.2; color: #6B6B6B; mso-line-height-alt: 14px;">
                                      <p style="font-size: 12px; line-height: 1.2; text-align: center; mso-line-height-alt: 17px; margin: 0;"> <img src="https://hrapp-email-images.s3.amazonaws.com/locationImage.png" style="width: 10px; height: auto;" /> ${companyName || ''}, ${street1 || ''}, ${street2 || ''}, ${cityName || ''} - ${pinCode || ''}, ${stateName || ''} - ${countryName || ''}</p>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>`;
     let documentsBucket=process.env.documentsBucket;

     let data=await getCustomEmailAttachment(organizationDbConnection, orgCode, documentsBucket, process.env.region, Employee_Id)
     let finalEmailBody = `
     <div style="background-color: #f5f5f5; padding: 20px; font-family: Arial, sans-serif;">
         <div style="max-width: 800px; margin: 0 auto; background-color: #FFFFFF; border-radius: 5px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
             <div style="padding: 20px; background-color: #2b42c4; color: white;">
                 <h1 style="margin: 0; font-size: 24px;">Travel Request Details</h1>
             </div>
             <div style="padding: 20px;">
                 ${logoHtml}
                 ${travelSummary}
                 ${flightTable}
                 ${hotelTable}
                 ${trainTable}
                 ${busTable}
                 ${carTable}
                 ${data}
                 <div style="margin: auto; text-align: center; padding-top: 20px; border-top: 1px solid #e2e7ee; font-size: 12px; color: #666;">
                     <p>If you have any questions regarding this travel request, please contact the HR support team.</p>
                 </div>
             </div>
             ${footerHtml}
         </div>
     </div>`;
     return{
      replyTo,
      finalEmailBody
     }

  } catch (e) {
      console.log("Error in travelDetailsTemplate main block", e);
      return '';
  }
}
const nodemailer = require("nodemailer");

async function sendCustomEmail(organizationDbConnection,email_body,email_subject,replyTo) {
    try {
        // Retrieve email details from the database
        const emailData = await organizationDbConnection('employee_travel_setting')
            .select('Email_Recipients','Additional_Recipients','Employee_Travel_Setting_Id')
            .orderBy('Employee_Travel_Setting_Id', 'desc') // Fetch the latest email settings
            .first();

        if (!emailData) {
            throw new Error("No email configuration found.");
        }

        const { Email_Recipients,Additional_Recipients } = emailData;
        const recipientEmails = JSON.parse(Email_Recipients);
        const additionalEmails=JSON.parse(Additional_Recipients);
        let originalSenderName = null;
          let result = await getSenderNameAndLogo(organizationDbConnection);
          originalSenderName = result?.senderName;
          let source= originalSenderName ? `${originalSenderName} <${process.env.emailFrom}>` : process.env.emailFrom;
        if (!recipientEmails.length) {
            throw new Error("No recipients specified.");
        }
       let region=process.env.sesRegion;
       const ses = new AWS.SES({ region });
        // Nodemailer transport with SES
        const transporter = nodemailer.createTransport({ SES: ses });

        // Email options
        const mailOptions = {
            from: source ?? process.env.emailFrom,
            subject: email_subject,
            html: email_body,
            to: recipientEmails?.join(",") ?? '',
            cc: additionalEmails?.join(",") ?? '',
            replyTo: replyTo || '',
        };
        // Send email
        const info = await transporter.sendMail(mailOptions);
        return info ? "Invited" : "Pending";

    } catch (error) {
        console.error("Error in sendCustomEmail:", error);
        throw error;
    }
}

async function getEmployeeEmailsByGroup(groupId, organizationDbConnection) {
  try {
    const emails = await organizationDbConnection('custom_employee_group_employees' + ' as CEGE')
      .pluck('EJ.Emp_Email')
      .innerJoin('custom_employee_group' + ' as CEG', 'CEGE.Group_Id', 'CEG.Group_Id')
      .leftJoin(ehrTables.empJob + ' as EJ', 'CEGE.Employee_Id', 'EJ.Employee_Id')
      .whereIn('CEGE.Type', ['Default', 'AdditionalInclusion'])
      .where('CEG.Group_Id', groupId)
      .andWhere(function() {
        this.whereNotNull('EJ.Emp_Email').andWhere('EJ.Emp_Email', '!=', '');
      })
      .where('EJ.Emp_Status', 'Active');

    return emails;
  } catch (error) {
    console.error("Error retrieving employee emails for group:", groupId, error);
    throw error;
  }
}
async function approvedAndForecastpositiondetails(data, orgCode, organizationDbConnection, context,orgDetails) {
  try {
      let companyName = "";
      companyName = orgDetails.orgName;
      let replyTo=orgDetails.hrAdminEmailAddress;
      // Get company logo
      let reportLogoS3Path = null;
      let result = await getSenderNameAndLogo(organizationDbConnection);
      let Add_Logo = result?.Add_Logo;
      if (Add_Logo?.toLowerCase() === 'no') {
          // Handle entomo case
      } else {
          reportLogoS3Path = orgDetails.Report_LogoPath ? process.env.domainName + '_upload/' + orgCode + '_tmp/logos/' + orgDetails.Report_LogoPath : '';
      }
      let logoHtml = '';
      if (reportLogoS3Path) {
          logoHtml = `<div style="text-align: center; margin-bottom: 20px;">
              <img src="${reportLogoS3Path}" alt="${companyName}" style="max-height: 80px; max-width: 200px;">
          </div>`;
      }

      // Create the main travel details summary section
      const approvedAndForecastpositiondetails = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto;">
          <p style="font-size: 16px; margin-bottom: 20px;">
              We are pleased to inform you that a new <strong>Approved & Forecasted Position</strong> has been requested and is ready for hiring action. Please find the details below:
          </p>
  
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 14px;">
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; width: 40%; font-weight: bold;">Position Code:</td>
                  <td style="padding: 10px 0;">
                    ${data?.positionCode ? `${data.positionTitle} - ${data.positionCode}` : 'N/A'}
                  </td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Department:</td>
                  <td style="padding: 10px 0;">${data.departmentName || 'N/A'}</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Division:</td>
                  <td style="padding: 10px 0;">${data.divisionName || 'N/A'}</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Group:</td>
                  <td style="padding: 10px 0;">${data.groupName || 'N/A'}</td>
              </tr>
              <tr>
                  <td style="padding: 10px 0; font-weight: bold;">Company:</td>
                  <td style="padding: 10px 0;">${companyName || 'N/A'}</td>
              </tr>
          </table>
  
          <p style="margin-bottom: 20px; font-size: 14px;">
              You are requested to <strong>initiate the hiring process</strong> for the above position at the earliest. Kindly ensure the position is posted on relevant platforms, and coordinate with the respective department head to proceed with the recruitment plan.
          </p>
  
          <p style="font-size: 14px;">
              If you have any questions or require further details, feel free to reach out.
          </p>
  
          <p style="margin-top: 20px; font-size: 14px;">
              Best regards,<br>
              ${data.Added_By_Name}
          </p>
      </div>
      `;
     let finalEmailBody = `
     <div style="background-color: #f5f5f5; padding: 20px; font-family: Arial, sans-serif; font-size: 14px;">
         <div style="max-width: 800px; margin: 0 auto; background-color: #FFFFFF; border-radius: 5px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
             <div style="padding: 20px;">
                 ${logoHtml}
                 ${approvedAndForecastpositiondetails}
             </div>
         </div>
     </div>`;
     return{
      replyTo,
      finalEmailBody
     }

  } catch (e) {
      console.log("Error in travelDetailsTemplate main block", e);
      return '';
  }
}
async function newpositiondetails(data, orgCode, organizationDbConnection, context,orgDetails) {
  try {
      let companyName = "";
      companyName = orgDetails.orgName;
      let replyTo=orgDetails.hrAdminEmailAddress;
      // Get company logo
      let reportLogoS3Path = null;
      let result = await getSenderNameAndLogo(organizationDbConnection);
      let Add_Logo = result?.Add_Logo;
      if (Add_Logo?.toLowerCase() === 'no') {
          // Handle entomo case
      } else {
          reportLogoS3Path = orgDetails.Report_LogoPath ? process.env.domainName + '_upload/' + orgCode + '_tmp/logos/' + orgDetails.Report_LogoPath : '';
      }
      let logoHtml = '';
      if (reportLogoS3Path) {
          logoHtml = `<div style="text-align: center; margin-bottom: 20px;">
              <img src="${reportLogoS3Path}" alt="${companyName}" style="max-height: 80px; max-width: 200px;">
          </div>`;
      }
       let approvedAndForecastpositiondetails;
      // Create the main travel details summary section
      if(!data.positionCode || data.positionCode === ''){
      approvedAndForecastpositiondetails = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto;">
          <p style="font-size: 14px; margin-bottom: 20px;">
             A <strong>Sourcing</strong> Request has been raised for a <strong>New Position</strong> and has been approved as part of our workforce planning and organizational growth strategy with the following details.
          </p>
  
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;font-size: 14px">
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; width: 40%; font-weight: bold;">Position Title:</td>
                  <td style="padding: 10px 0;">
                  ${data.positionTitle}
                 </td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Department:</td>
                  <td style="padding: 10px 0;">${data.departmentName || 'N/A'}</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Division:</td>
                  <td style="padding: 10px 0;">${data.divisionName || 'N/A'}</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Group:</td>
                  <td style="padding: 10px 0;">${data.groupName || 'N/A'}</td>
              </tr>
              <tr>
                  <td style="padding: 10px 0; font-weight: bold;">Company:</td>
                  <td style="padding: 10px 0;">${companyName || 'N/A'}</td>
              </tr>
          </table>
  
       <p style="margin-bottom: 20px;font-size: 14px">
      As the ${data.positionTitle} is yet to be assigned, this request has been initiated to commence sourcing activities proactively. You are requested to <strong>initiate the hiring process</strong> in coordination with the respective department stakeholders. Please gather specific role expectations, required skills, and a timeline for hiring to proceed with sourcing the right candidates. If you need any additional inputs or clarification, feel free to connect with the position requester or the HR team. Thank you for your support in ensuring a smooth hiring process.
       </p>
          <p style="margin-top: 20px;font-size: 14px">
              Best regards,<br>
              ${data.Added_By_Name}
          </p>
      </div>
      `;
      }else{
        approvedAndForecastpositiondetails = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto;">
          <p style="font-size: 14px; margin-bottom: 20px;">
            We would like to inform you that an Additional Manning Request has been approved for the following position:
          </p>
  
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;font-size: 14px">
           <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; width: 40%; font-weight: bold;">Position Code:</td>
                  <td style="padding: 10px 0;">
                  ${data.positionCode}
                 </td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; width: 40%; font-weight: bold;">Position Title:</td>
                  <td style="padding: 10px 0;">
                  ${data.positionTitle}
                 </td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Department:</td>
                  <td style="padding: 10px 0;">${data.departmentName || 'N/A'}</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Division:</td>
                  <td style="padding: 10px 0;">${data.divisionName || 'N/A'}</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Group:</td>
                  <td style="padding: 10px 0;">${data.groupName || 'N/A'}</td>
              </tr>
              <tr>
                  <td style="padding: 10px 0; font-weight: bold;">Company:</td>
                  <td style="padding: 10px 0;">${companyName || 'N/A'}</td>
              </tr>
          </table>
  
       <p style="margin-bottom: 20px;font-size: 14px">
     This request is aligned with the increased workload and business needs of the department.
Please proceed to initiate the hiring process for the approved additional headcount. Ensure that the sourcing strategy aligns with the required timelines and role expectations
       </p>
        <p style="font-size: 14px">
              If you have any questions or require further details, feel free to reach out.
          </p>
          <p style="margin-top: 20px;font-size: 14px">
              Best regards,<br>
              ${data.Added_By_Name}
          </p>
      </div>
      `;
      }
     let finalEmailBody = `
     <div style="background-color: #f5f5f5; padding: 20px; font-family: Arial, sans-serif;font-size: 14px">
         <div style="max-width: 800px; margin: 0 auto; background-color: #FFFFFF; border-radius: 5px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
             <div style="padding: 20px;">
                 ${logoHtml}
                 ${approvedAndForecastpositiondetails}
             </div>
         </div>
     </div>`;
     return{
      replyTo,
      finalEmailBody
     }

  } catch (e) {
      console.log("Error in travelDetailsTemplate main block", e);
      return '';
  }
}
async function sendApprovedPositionEmail(
  organizationDbConnection,
  email_body,
  email_subject,
  replyTo,
  instanceData
) {
  try {
    // Retrieve email details from the database
    let emailData = await getEmployeeEmailsByGroup(
      instanceData.customGroupId,
      organizationDbConnection
    )
    if (!emailData || emailData.length === 0) {
      return
    }
    let originalSenderName = null
    let result = await getSenderNameAndLogo(organizationDbConnection)
    originalSenderName = result?.senderName
    let source = originalSenderName
      ? `${originalSenderName} <${process.env.emailFrom}>`
      : process.env.emailFrom
    let region = process.env.sesRegion
    const ses = new AWS.SES({ region })
    // Nodemailer transport with SES
    const transporter = nodemailer.createTransport({ SES: ses })

    // Email options
    const mailOptions = {
      from: source ?? process.env.emailFrom,
      subject: email_subject,
      html: email_body,
      to: emailData,
      cc: instanceData.Added_By_Email || '',
      replyTo: replyTo || ''
    }

    // Send email
    const info = await transporter.sendMail(mailOptions)
    return info ? 'Invited' : 'Pending'
  } catch (error) {
    console.error('Error in sendApprovedPositionEmail:', error)
    throw error
  }
}
async function getSenderNameAndLogo(organizationDbConnection) {
  try {
      let data = await organizationDbConnection('email_notification_setting')
          .select('Sender_Name as senderName','Add_Logo')
          .first();

      if (data) {
          return data;
      }
      return false
  }
  catch (err) {
      console.log('Error in getSenderName function.', err);
      return false
  }
}
async function getEmployeeEmail(employeeId, organizationDbConnection) {
  const emails = await organizationDbConnection(ehrTables.empJob + ' as EJ')
    .select(
      'EJ.Emp_Email',
      organizationDbConnection.raw(`
        CONCAT_WS(" ", EP_U.Emp_First_Name, EP_U.Emp_Middle_Name, EP_U.Emp_Last_Name) as Added_By_Name
      `)
    )
    .where('EJ.Employee_Id', employeeId)
    .andWhereNot('EJ.Emp_Email', '')
    .andWhereNot('EJ.Emp_Email', null)
    .leftJoin(
      `${ehrTables.empPersonalInfo} as EP_U`,
      'EP_U.Employee_Id',
      'EJ.Employee_Id'
    )

  return emails
}
module.exports = {
  // common util functions
  getError,
  getFormattedDateString,
  getFileURL,
  // master's related functions
  getAllDesignation,
  getAllDepartment,
  getAllEmployeeType,
  getAllLocation,
  // employee related funtions
  checkEmployeeAccessRights,
  getEmployeeTimeZone,
  getTrackingFieldName,
  createSystemLogActivities,
  getEmployeeRelievingReaason,
  getEmailNotificationEmployeeDetails,
  sendEmailNotifications,
  getOrganizationSettingsDetails,
  updateStatusAndAssetMapping,
  updateWorkflowResignationInstanceData,
  updateWorkflowLeaveInstanceData,
  revokeEmployeePotalAccess,
  callCamuResignationEndpoint,
  employeeSettlementInitiated,
  updatePreApprovalRequestInstanceData,
  retrieveEmployeeTravel,
  travelDetailsTemplate,
  sendCustomEmail,
  sendApprovedPositionEmail,
  approvedAndForecastpositiondetails,
  getEmployeeEmail,
  newpositiondetails
};
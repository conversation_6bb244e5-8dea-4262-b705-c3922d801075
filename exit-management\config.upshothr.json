{"securityGroupIds": ["sg-04bc41f567ae532b6"], "subnetIds": ["subnet-0cbbb2391d86f1d0e", "subnet-0120a712c5936a292"], "secretName": "PROD/UPSHOT/PGACCESS", "firebaseAuthorizer": "arn:aws:lambda:eu-west-2:327313496531:function:ATS-upshothr-firebaseauthorizer", "role": "arn:aws:iam::327313496531:role/lambdaFullAccess", "dbPrefix": "upshothr_", "hrappProfileBucket": "s3.images.upshothr.uk", "domainName": "upshothr", "workflowEngineUrl": "https://api.upshothr.uk/workflowEngine", "customDomainName": "api.upshothr.uk", "logoBucket": "s3.logos.upshothr.uk", "emailFrom": "<EMAIL>", "sesRegion": "eu-west-2", "webAddress": ".uk", "leaveStatusDomainName": "upshothr.uk", "camuExitStaffEndPoint": "external/staff/exit", "asyncSyntrumAPIStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-processAirTicketSummary", "attendanceSummaryProcess": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-attendanceSummaryStepFunction", "syntrumLeaveIntegrationProcess": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-pushLeaveToSyntrum"}
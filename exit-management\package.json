{"name": "exit-management", "version": "1.0.0", "description": "exit-management related api services", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "keywords": ["exit management, resignation"], "author": "Caprice Technologies Pvt Ltd", "license": "ISC", "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "@haftahave/serverless-ses-template": "^3.0.1", "apollo-server-lambda": "^2.15.0", "aws-sdk": "^2.647.0", "axios": "^0.19.0", "form-data": "^4.0.0", "fs": "^0.0.1-security", "graphql": "^14.5.6", "kms-json": "^1.1.1", "knex": "2.3.0", "moment": "^2.24.0", "moment-timezone": "^0.5.26", "mysql": "^2.17.1", "nodemailer": "^6.10.0", "serverless-domain-manager": "^7.1.2", "serverless-prune-plugin": "^2.0.2"}, "devDependencies": {"serverless-offline": "^13.2.0"}}
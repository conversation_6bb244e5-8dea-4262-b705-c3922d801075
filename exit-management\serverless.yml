service: HRAPPExitManagement # service name

plugins:
    - '@haftahave/serverless-ses-template'
    - serverless-domain-manager
    - serverless-prune-plugin
    - serverless-offline
provider:
    name: aws
    runtime: nodejs18.x
    memorySize: 1024
    timeout: 29
    stage: ${opt:stage} # get stage name dynamically
    region : ${opt:region} # deployed region name
    role: ${file(./config.${self:provider.stage}.json):role} # Assign role to the lambda functions
    environment: # Common environment variables
        dbPrefix: ${file(./config.${self:provider.stage}.json):dbPrefix}
        secretName: ${file(config.${self:provider.stage}.json):secretName}
        stageName: ${self:provider.stage}
        region : ${self:provider.region}
        hrappProfileBucket : ${file(config.${self:provider.stage}.json):hrappProfileBucket}
        domainName : ${file(config.${self:provider.stage}.json):domainName}
        workflowEngineUrl: ${file(config.${self:provider.stage}.json):workflowEngineUrl}
        customDomainName: ${file(config.${self:provider.stage}.json):customDomainName}
    vpc:
        securityGroupIds: ${file(./config.${self:provider.stage}.json):securityGroupIds}
        subnetIds: ${file(./config.${self:provider.stage}.json):subnetIds}
custom:
    sesTemplates:
        configFile: './ses-email-templates/index.js' # Config file path (default './ses-email-templates/index.js')
        disableAutoDeploy: false  # Specifies whether to sync templates while sls deploy (default false)
        region: ${file(./config.${self:provider.stage}.json):sesRegion} # Specifies AWS region for SES templates
    customDomain:
        domainName: ${file(./config.${self:provider.stage}.json):customDomainName}
        basePath: 'exitManagement'
        stage: ${self:provider.stage}
        createRoute53Record: true
        endpointType: 'edge'
    prune:
        automatic: true
        number: 3
functions:
    exitManagement:
        handler: src/handler.graphql
        events:
            - http:
                path: exitManagement
                method: post
                cors:
                    origin: '*'
                    headers:
                      - Content-Type
                      - X-Amz-Date
                      - X-Api-Key
                      - X-Amz-Security-Token
                      - X-Amz-User-Agent
                      - authorization
                      - org_code
                      - refresh_token
                      - partnerid
                      - user_ip
                      - additional_headers
                    allowCredentials: false
                authorizer:
                    arn: ${file(config.${self:provider.stage}.json):firebaseAuthorizer}
                    resultTtlInSeconds: 0
                    type: request
        environment:
            roleAdd : Role_Add
            roleUpdate : Role_Update
            roleDelete : Role_Delete
            roleView : Role_View
            systemlogDelete: Delete
            systemlogAdd: Add
            systemlogEdit: Update
            sesRegion: ${self:custom.sesTemplates.region}
            emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
            logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
            domainName: ${file(config.${self:provider.stage}.json):domainName}
            webAddress: ${file(config.${self:provider.stage}.json):webAddress}
            camuExitStaffEndPoint: ${file(config.${self:provider.stage}.json):camuExitStaffEndPoint}
            asyncSyntrumAPIStepFunction: ${file(config.${self:provider.stage}.json):asyncSyntrumAPIStepFunction}
            processAirTicketSummary: ${file(config.${self:provider.stage}.json):processAirTicketSummary}
    resignationEndpoint:
        handler: src/workflow.resignationEndpoint
        events:
            - http:
                path: resignationendpoint
                method: post
                cors: true
        environment:
            camuExitStaffEndPoint: ${file(config.${self:provider.stage}.json):camuExitStaffEndPoint}
            asyncSyntrumAPIStepFunction: ${file(config.${self:provider.stage}.json):asyncSyntrumAPIStepFunction}
            processAirTicketSummary: ${file(config.${self:provider.stage}.json):processAirTicketSummary}

    leaveEndpoint:
        handler: src/workflow.leaveEndpoint
        events:
            - http:
                path: leaveEndpoint
                method: post
                cors: true
        environment: # Common environment variables
          leaveStatusDomainName: ${file(./config.${self:provider.stage}.json):leaveStatusDomainName}
          attendanceSummaryProcess: ${file(config.${self:provider.stage}.json):attendanceSummaryProcess}
          syntrumLeaveIntegrationProcess: ${file(config.${self:provider.stage}.json):syntrumLeaveIntegrationProcess}

    handleLeaveApprovalOrchestrator:
        handler: src/handleLeaveApprovalOrchestrator.handleLeaveApprovalOrchestrator
        name: handle-leave-approval-orchestrator
        timeout: 900 # Lambda timeout
        environment: # environment variables
            attendanceSummaryProcess: ${file(config.${self:provider.stage}.json):attendanceSummaryProcess}
            syntrumLeaveIntegrationProcess: ${file(config.${self:provider.stage}.json):syntrumLeaveIntegrationProcess}

    preApprovalEndpoint:
        handler: src/workflow.preApprovalEndpoint
        events:
            - http:
                path: preApprovalEndpoint
                method: post
                cors: true

    lopRecoveryEndpoint:
        handler: src/workflow.lopRecoveryEndpoint
        events:
            - http:
                path: lopRecoveryEndpoint
                method: post
                cors: true

    reimbursementEndpoint:
        handler: src/workflow.reimbursementEndpoint
        events:
            - http:
                path: reimbursementEndpoint
                method: post
                cors: true

    salaryRevisionStatusUpdateEndpoint:
        handler: src/workflow.salaryRevisionStatusUpdateEndpoint
        events:
            - http:
                path: salaryRevisionStatusUpdateEndpoint
                method: post
                cors: true

    timesheetStatusUpdateEndpoint:
        handler: src/workflow.timesheetStatusUpdateEndpoint
        events:
            - http:
                path: timesheetStatusUpdateEndpoint
                method: post
                cors: true

    recruitmentPosStatusUpdate:
        handler: src/workflow.recruitmentPosStatusUpdate
        events:
            - http:
                path: recruitmentPosStatusUpdate
                method: post
                cors: true
        environment:
            sesRegion: ${self:custom.sesTemplates.region}
            region : ${self:provider.region}
            emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
            documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
            hrappProfileBucket : ${file(config.${self:provider.stage}.json):hrappProfileBucket}

    empTravelStatusUpdateEndpoint:
        handler: src/workflow.empTravelStatusUpdateEndpoint
        events:
            - http:
                path: empTravelStatusUpdateEndpoint
                method: post
                cors: true
        environment:
            sesRegion: ${self:custom.sesTemplates.region}
            region : ${self:provider.region}
            emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
            documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
            hrappProfileBucket : ${file(config.${self:provider.stage}.json):hrappProfileBucket}
            
    compOffEndpoint:
        handler: src/workflow.compOffEndpoint
        events:
            - http:
                path: compOffEndpoint
                method: post
                cors: true

    shortTimeOffEndpoint:
        handler: src/workflow.shortTimeOffEndpoint
        events:
            - http:
                path: shortTimeOffEndpoint
                method: post
                cors: true
        environment:
            attendanceSummaryProcess: ${file(config.${self:provider.stage}.json):attendanceSummaryProcess}

    newPositionStatusUpdateEndpoint:
        handler: src/workflow.newPositionStatusUpdateEndpoint
        events:
            - http:
                path: newPositionStatusUpdateEndpoint
                method: post
                cors: true
        environment:
            sesRegion: ${self:custom.sesTemplates.region}
            region : ${self:provider.region}
            emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
            documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
            hrappProfileBucket : ${file(config.${self:provider.stage}.json):hrappProfileBucket}

    getDbProperties:
        handler: src/workflow.getDbProperties
        events:
            - http:
                path: getdbproperties
                method: post
                cors: true

    getEmployeeManagerId:
      handler: src/workflow.getEmployeeManagerId
      events:
        - http:
            path: getEmployeeManagerId
            method: post
            cors: true

    getSecondLineManager:
      handler: src/workflow.getSecondLineManager
      events:
        - http:
            path: getSecondLineManager
            method: post
            cors: true

resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}   

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
            gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
            gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId: 
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: "{ \"message\": {\"message\": \"Forbidden.\" } }"
    
    GatewayResponse401: # statusCode 401
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
            gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
            gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId: 
          Ref: 'ApiGatewayRestApi'
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: '401' # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: "{ \"message\": {\"message\": \"Unauthorized request.\" } }"

    GatewayResponse5XX: # statusCode 5XX series errorcode
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId: 
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: "{ \"message\": {\"message\": \"API gateway timeout.\" } }"

{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "secretName": "PROD/CANNY/PGACCESS", "firebaseAuthorizer": "arn:aws:lambda:ap-south-1:378423228887:function:ATS-cannyhr-firebaseauthorizer", "role": "arn:aws:iam::378423228887:role/lambdaFullAccess", "dbPrefix": "cannyhr_", "hrappProfileBucket": "s3.images.cannyhr.com", "domainName": "cannyhr", "workflowEngineUrl": "https://api.cannyhr.com/workflowEngine", "customDomainName": "api.cannyhr.com", "logoBucket": "s3.logos.cannyhr.com", "documentsBucket": "s3.taxdocs.cannyhr.com", "emailFrom": "<EMAIL>", "sesRegion": "us-east-1", "region": "ap-south-1", "webAddress": ".com", "leaveStatusDomainName": "cannyhr.com", "camuExitStaffEndPoint": "external/staff/exit", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-processAirTicketSummary", "attendanceSummaryProcess": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-attendanceSummaryStepFunction", "syntrumLeaveIntegrationProcess": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-pushLeaveToSyntrum"}
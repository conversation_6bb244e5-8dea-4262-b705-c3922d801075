//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');
const moment = require('moment');
const { validateAndTriggerAttendanceSummary } = require('./leaveApprovalProcessor');
const { syntrumValues } = require("../common/AppConstants");

module.exports.handleLeaveApprovalOrchestrator = async (event, context) => {
    console.log('Inside handleLeaveApprovalOrchestration function',event);
    let organizationDbConnection;
    try {
        // parse event body
        let args = JSON.parse(event.body);
        console.log("args",args)

        let { approvalInputs, orgCode, leaveApprovalInputs } = args;

        const { getOrganizationDbConnection } = require('./helper');

        organizationDbConnection = await getOrganizationDbConnection( orgCode );
        let { syntrumInputs,summaryInputs,replenishmentInputs } = approvalInputs;
        
        await Promise.all([
            handleSyntrumLeaveIntegration(organizationDbConnection,syntrumInputs),
            triggerAttendanceSummaryEndpoint(organizationDbConnection,summaryInputs),
            replenishmentDuringLeaveApproval(organizationDbConnection,replenishmentInputs,leaveApprovalInputs)
        ]);

        return(callbackResponse('','success'));
    }catch(mainCatchError){
        console.log('Error in the handleLeaveApprovalOrchestrator function main catch block.',mainCatchError,event);
        return(callbackResponse('error',''));
    }finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}

function callbackResponse(isSuccess){
     // Form and return response
     const response = {
        errorCode: 200,
        headers: {
            'Content-Type': '*/*',
            'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
            success: isSuccess
        })
    };
    return (response);
}

async function triggerAttendanceSummaryEndpoint(organizationDbConnection,otherDetails){
    console.log('Inside triggerAttendanceSummaryEndpoint function',otherDetails)
    try{
        otherDetails.tableName = ehrTables.empLeaves;
        await validateAndTriggerAttendanceSummary(organizationDbConnection,otherDetails);
        return 1;
    }catch(error){
        console.error('Error in triggerAttendanceSummaryEndpoint function main catch block', error, otherDetails);
        return 0;
    }
}

async function replenishmentDuringLeaveApproval(organizationDbConnection,replenishmentInputs,leaveApprovalInputs) {
    console.log('Inside replenishmentDuringLeaveApproval function',replenishmentInputs);
    let runReplenishmentClosure = 0;

    try {
        let { eligibleLeaveIds,leaveDetailsGroupByUId,  leaveReplenishmentEmployeeIds,
            leaveReplenishmentLeaveTypeIds  } = replenishmentInputs;

        let { completedBy,currentDateTime } = leaveApprovalInputs;
    
        if(eligibleLeaveIds?.length){
            let employeeEligibleLeaveDetails = [];
            let existingEligibleLeaveDetails = [];
            let maternityLeaveSlabsGroupByUId = {};
            let employeeDependentGroupByUId = {};
            let childCountResult='';
            let maternityLeaveSlabs = '';

            let allEmployeeIds = leaveReplenishmentEmployeeIds;
            let allLeaveTypeIds = leaveReplenishmentLeaveTypeIds;
            let matchedLeaveIds = eligibleLeaveIds;
            
            if(allLeaveTypeIds?.length){
                maternityLeaveSlabs = await organizationDbConnection(ehrTables.maternitySlab)
                                    .select('Total_Days', 'Child_From', 'Child_To','LeaveType_Id')
                                    .whereIn('LeaveType_Id',allLeaveTypeIds);
                maternityLeaveSlabsGroupByUId = await commonLib.func.organizeData(maternityLeaveSlabs,'LeaveType_Id');
            }
            if(allEmployeeIds?.length){
                // Count the number of dependents (Son or Daughter)
                childCountResult = await organizationDbConnection(ehrTables.empDependent)
                                        .select('Employee_Id')
                                        .count('Dependent_Id as Child_Count')
                                        .whereIn('Employee_Id', allEmployeeIds)
                                        .whereIn('Relationship', ['Son', 'Daughter'])
                                        .groupBy('Employee_Id');
                employeeDependentGroupByUId = await commonLib.func.organizeData(childCountResult,'Employee_Id');
            }

            let currentLeaveBalanceDetails = await getEmployeeEligibleLeaveDetails(organizationDbConnection,allEmployeeIds,allLeaveTypeIds);
            let allEmployeeCurrentLeaveBalanceDetails = await commonLib.func.organizeData(currentLeaveBalanceDetails,'Employee_Id','LeaveType_Id');

            console.log("allEmployeeCurrentLeaveBalanceDetails",allEmployeeCurrentLeaveBalanceDetails);

            console.log("Replenishment Debug Info => allLeaveTypeIds:", allLeaveTypeIds, "matchedLeaveIds:", matchedLeaveIds, "allEmployeeIds:", allEmployeeIds, "childCountResult:", childCountResult, "maternityLeaveSlabs:", maternityLeaveSlabs, "maternityLeaveSlabsGroupByUId:", maternityLeaveSlabsGroupByUId);
            for (const leaveId of matchedLeaveIds) {
                let employeeLeaveRequestDetails = leaveDetailsGroupByUId[leaveId]?.[0] ?? null;
                let newLimit = 0;
                let pushNewEligiblityDetails = 0;

                if(!employeeLeaveRequestDetails){
                    console.log('Employee leave details not exist',leaveId);
                    continue;
                }

                let { Leave_Closure_Based_On, Leave_Enforcement_Configuration, encashedDays,End_Date, leaveTypeReplenishmentLimit, employeeReplenishmentLimit, Total_Days:leaveTypeTotalDays, Employee_Id: employeeId, LeaveType_Id: leaveTypeId, Leaves_Taken: leavesTaken, Last_CO_Balance: lastCoBalance, Leave_Balance: leaveBalance,No_Of_Days:totalCarryOverDays,Eligible_Days: currentYearEmployeeLeaveEligibility } = employeeLeaveRequestDetails;
                if (Leave_Closure_Based_On === 'Limited Replenishment on Approval' && leaveTypeReplenishmentLimit) {
                    employeeReplenishmentLimit = Number.isFinite(employeeReplenishmentLimit) ? employeeReplenishmentLimit : 0;
                    leaveTypeReplenishmentLimit = Number.isFinite(leaveTypeReplenishmentLimit) ? leaveTypeReplenishmentLimit : 0;
                    if (employeeReplenishmentLimit < leaveTypeReplenishmentLimit) {
                        newLimit = employeeReplenishmentLimit + 1;
                        pushNewEligiblityDetails = 1;
                    }else{
                        console.log('employeeReplenishmentLimit is greater than or equal to leaveTypeReplenishment limit',employeeReplenishmentLimit,leaveTypeReplenishmentLimit,leaveId);
                    }
                } else if (Leave_Closure_Based_On === 'Unlimited Replenishment on Approval') {
                    pushNewEligiblityDetails = 1;
                }else{
                    console.log('Leave Type not applicable for leave replenishment');
                }

                if (pushNewEligiblityDetails) {
                    let currentLeaveBalanceKey = `${employeeId}|${leaveTypeId}`;
                    let currentLeaveBalanceDetails = allEmployeeCurrentLeaveBalanceDetails[currentLeaveBalanceKey]?.[0] ?? null;

                    // Check employee ID and Leave Type ID existence
                    if (currentLeaveBalanceDetails && Object.keys(currentLeaveBalanceDetails).length > 0) {
                        leavesTaken = currentLeaveBalanceDetails['Leaves_Taken'];
                        lastCoBalance = currentLeaveBalanceDetails['Last_CO_Balance'];
                        leaveBalance = currentLeaveBalanceDetails['Leave_Balance'];
                    
                        console.log('currentLeaveBalanceDetails',currentLeaveBalanceDetails)

                        let currentLeaveBalance = (totalCarryOverDays + currentYearEmployeeLeaveEligibility) - (encashedDays + leavesTaken);
                        
                        if( !currentLeaveBalance || parseFloat(currentLeaveBalance) === 0.0){
                            let isReplenishmentApplicable = 0;
                            let totalDays = 0;

                            if (Leave_Enforcement_Configuration === 1) {
                                totalDays = leaveTypeTotalDays;
                                isReplenishmentApplicable= 1;
                            } else if (Leave_Enforcement_Configuration === 4) {
                                let employeeDependentDetail = employeeDependentGroupByUId[employeeId]?.[0] ?? null;
                                let leaveTypeMaternitySlabDetails = maternityLeaveSlabsGroupByUId[leaveTypeId] ?? null;
                                if(employeeDependentDetail && Object.keys(employeeDependentDetail).length && leaveTypeMaternitySlabDetails?.length){
                                    totalDays = await calculateMaternityLeaveSlabTotalDays(employeeDependentDetail,leaveTypeMaternitySlabDetails);
                                    isReplenishmentApplicable = 1;
                                }else{
                                    console.log("Empty dependent and maternity slab",employeeDependentGroupByUId,maternityLeaveSlabsGroupByUId,"employeeDependentDetail",employeeDependentDetail,"leaveTypeMaternitySlabDetails",leaveTypeMaternitySlabDetails);
                                }
                            }
                            if(isReplenishmentApplicable == 1){
                                const leaveClosureStartDate = moment(End_Date).add(1, 'day').format('YYYY-MM-DD');
                                const leaveClosureDetails = await replenishmentLeaveClosureDate(leaveClosureStartDate);
                    
                                if (Object.keys(leaveClosureDetails).length > 0) {
                                    const newRecord = {
                                        LeaveType_Id: leaveTypeId,
                                        Employee_Id: employeeId,
                                        Eligible_Days: totalDays > 0 ? totalDays : 0,
                                        Replenishment_Limit: employeeReplenishmentLimit,
                                        CO_Year: leaveClosureDetails.coYear,
                                        LE_Year: leaveClosureDetails.coYear,
                                        Leave_Closure_Start_Date: leaveClosureStartDate,
                                        Leave_Closure_End_Date: leaveClosureDetails.leaveClosureEndDate
                                    };
                                    employeeEligibleLeaveDetails.push(newRecord);
                                    existingEligibleLeaveDetails.push(currentLeaveBalanceDetails);
                                }else{
                                    console.log('Empty leave closure details.',leaveClosureDetails,leaveClosureStartDate,leaveId,employeeLeaveRequestDetails)
                                }
                            }else{
                                console.log('Replenishment is not applicable.',isReplenishmentApplicable);
                            }
                        }else{
                            console.log('Current leave balance is greater than zero',currentLeaveBalance,currentLeaveBalanceDetails);
                        }
                    }else{
                        console.log('Empty currentLeaveBalanceDetails',currentLeaveBalanceDetails,currentLeaveBalanceKey); 
                    }
                }
            }
            console.log('employeeEligibleLeaveDetails',employeeEligibleLeaveDetails);
            if(employeeEligibleLeaveDetails?.length){
                let updateInputs = {  newEligibleLeaveDetails: employeeEligibleLeaveDetails,
                                      action: 'leave-closure',
                                      completedBy, 
                                      currentDateTime,
                                      existingEligibleLeaveDetails };

                await insertEligibleLeaveDetails(organizationDbConnection, updateInputs); 
                runReplenishmentClosure = 1;
            }
        }else{
            console.log('Replenishment leave ids are empty');
        }
    } catch (err) {
        console.error('Error in replenishmentDuringLeaveApproval:', err);
    }

    return runReplenishmentClosure;
}


async function calculateMaternityLeaveSlabTotalDays(employeeDependentDetail,leaveTypeMaternitySlabDetails) {
    try {
        let childRange =  Number(employeeDependentDetail?.Child_Count) || 0 + 1;
        const matchingRow = leaveTypeMaternitySlabDetails.find(row => {
            const from = parseInt(row.Child_From, 10);
            const to = row.Child_To === null ? Infinity : parseInt(row.Child_To, 10);
            return childRange >= from && childRange <= to;
        });
            
        const totalDays = matchingRow ? matchingRow.Total_Days : 0;
        if(totalDays<=0)
            console.log('No matching slab found', leaveTypeMaternitySlabDetails,employeeDependentDetail);
        
        return totalDays;
    } catch (error) {
        console.error('Error in calculateMaternityLeaveSlabTotalDays:', error);
        return 0;
    }
}

async function replenishmentLeaveClosureDate(leaveClosureStartDate, leaveClosureEndYear = 50) {
    console.log('Inside replenishmentLeaveClosureDate function');
    let leaveClosureDetails = {};
    try {
        if (leaveClosureStartDate) {
            const startDate = moment(leaveClosureStartDate);
            const end = startDate.clone().add(leaveClosureEndYear, 'years').subtract(1, 'day');
            let leaveClosureEndDate = end.format('YYYY-MM-DD');
            leaveClosureDetails.leaveClosureStartDate= leaveClosureStartDate;
            leaveClosureDetails.leaveClosureEndDate= leaveClosureEndDate;
            leaveClosureDetails.coYear = startDate.format('YYYY');
            return leaveClosureDetails;
        }else{
            console.error('Empty leaveClosureStartDate in replenishmentLeaveClosureDate function', leaveClosureStartDate);
        }
        return leaveClosureDetails;
    } catch (error) {
        console.error('Error in replenishmentLeaveClosureDate:', error);
        return {};
    }
}

async function handleSyntrumLeaveIntegration(organizationDbConnection,syntrumInputs){
    console.log('Inside handleSyntrumLeaveIntegration function',syntrumInputs)
    try{
        if(syntrumInputs.syntrumLeaveTypeIds?.length){
            let syntrumLeaveTypeIds = [...new Set(syntrumInputs.syntrumLeaveTypeIds)];
            syntrumInputs.syntrumLeaveTypeIds=syntrumLeaveTypeIds;
            
            let leavePaymentSlabs = await commonLib.employees.getLeavePaymentSlabs(organizationDbConnection, syntrumLeaveTypeIds);
    
            let apiIntegrationDetails = await organizationDbConnection(ehrTables.externalApiSyncDetails)
                                        .select('Entity_Type')
                                        .where('Integration_Type', syntrumValues.integrationType)
                                        .where('Sync_Direction', syntrumValues.syncDirection)
                                        .whereIn('Action', ['Approve','Cancel'])      
                                        .where('Status', 'Active');
            const availableEntityTypes = [
                ...new Set(apiIntegrationDetails.map(record => String(record.Entity_Type).trim()))
            ];                            
    
            syntrumInputs.availableEntityTypes=availableEntityTypes;
            syntrumInputs.leavePaymentSlabs=leavePaymentSlabs;
            
            console.log('Syntrum Inputs', syntrumInputs);
            await Promise.all([
                processLeaveAction(organizationDbConnection, syntrumInputs, 'Approve'),
                processLeaveAction(organizationDbConnection, syntrumInputs, 'Cancel')
            ]);
    
            return 1;
        }else{
            console.log("Syntrum leave action is empty in handleSyntrumLeaveIntegration");
            return 0;
        }
    }catch(error){
        console.error('Error in handleSyntrumLeaveIntegration function main catch block', error);
        return 0;
    }
}

async function processLeaveAction(organizationDbConnection,syntrumInputs, actionType) {
    console.log('Inside processLeaveAction function', actionType);
    try{
        let { syntrumLeaveTypeIds, availableEntityTypes, orgCode,currentDateTime } = syntrumInputs;

        if(syntrumLeaveTypeIds?.length && availableEntityTypes?.length){
            let { applicableSyntrumLeaveIds } = await commonLib.employees.determineSyntrumLeaveTypeCode(syntrumInputs,actionType);
            console.log('applicableSyntrumLeaveIds',applicableSyntrumLeaveIds,actionType)
            
            for (const entry of applicableSyntrumLeaveIds) {
                const entityType = entry.entityType;
              
                const entityTypeToArray = entityType.split(',').map(item => item.trim());

                if(entityType){
                    let syntrumApiInputs = {
                        action: actionType,
                        orgCode: orgCode,
                        entityType: (Array.isArray(entityTypeToArray) && entityTypeToArray.length>1) ? '' : entityType,
                        uniqueIds: entry.leaveIds,
                        uniqueIdDetails: ''
                    };
                    await triggerSyntrumLeaveEndpoint(organizationDbConnection,syntrumApiInputs,currentDateTime);
                } else{
                    console.error('Empty entityType in processLeaveAction function', entry,entityType);
                }  
            }
        }else{
            console.error('Empty syntrumLeaveTypeIds and availableEntityTypes in processLeaveAction function');
        }
        return 1;
    }catch(error){
        console.error('Error in processLeaveAction function main catch block', error);
        return 0;
    }
}

async function triggerSyntrumLeaveEndpoint(organizationDbConnection,syntrumInputs,currentDateTime){
    console.log('Inside triggerSyntrumLeaveEndpoint function',syntrumInputs)
    try{
        let triggerStepFunctionResponse = await commonLib.stepFunctions.triggerStepFunction(process.env.syntrumLeaveIntegrationProcess, 'pushLeaveToSyntrum', '', syntrumInputs); 
        if(!triggerStepFunctionResponse){
            console.log(`Error in triggering the syntrum step function`);
            const failureLogInputs = {
                Status: 'Failed',
                Action: syntrumInputs.action,
                Entity_Type: JSON.stringify(syntrumInputs.entityType),
                Entity_Id: syntrumInputs.uniqueIds[0],
                Form_Data:  JSON.stringify({}),
                Added_On: currentDateTime,
                Added_By: 0,
                Failure_Reason: JSON.stringify({ errorCode: 'UNKNOWN_CODE', errorMessage: 'Unknown error occurred.' }),
                Integration_Type: syntrumValues.integrationType
            };
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(failureLogInputs);
        }

        return 1;
    }catch(error){
        console.error('Error in triggerSyntrumLeaveEndpoint function main catch block', error);
        return 0;
    }
}
  
async function insertEligibleLeaveDetails(organizationDbConnection, updateInputs) {
    console.log('Inside insertEligibleLeaveDetails function',updateInputs);
    try{
        let {  newEligibleLeaveDetails, action, completedBy, currentDateTime, existingEligibleLeaveDetails } = updateInputs;
        if (newEligibleLeaveDetails.length > 0) {
            return organizationDbConnection.transaction(async (trx) => {
                const auditEmployeeEligibleLeave = [];
                const eligibleLeaveIds = [];

                if (existingEligibleLeaveDetails?.length > 0) {
                    existingEligibleLeaveDetails.forEach(row => {
                        auditEmployeeEligibleLeave.push({
                            LeaveType_Id: row.LeaveType_Id,
                            Employee_Id: row.Employee_Id,
                            Eligible_Days: row.Eligible_Days,
                            Leaves_Taken: row.Leaves_Taken,
                            Leave_Balance: row.Leave_Balance,
                            No_Of_Days: row.No_Of_Days,
                            Last_CO_Balance: row.Last_CO_Balance,
                            Accumulation_Lapsed_Days: row.Accumulation_Lapsed_Days,
                            Replenishment_Limit: row.Replenishment_Limit,
                            CO_Year: row.CO_Year,
                            LE_Year: row.LE_Year,
                            Leave_Closure_Start_Date: row.Leave_Closure_Start_Date,
                            Leave_Closure_End_Date: row.Leave_Closure_End_Date,
                            Audit_Reason: action,
                            Added_On: currentDateTime,
                            Added_By: completedBy || 0 //From leave approval we will get the completed by id use it
                        });

                        eligibleLeaveIds.push(row.Eligible_Leave_Id);
                    });
                    
                    if (auditEmployeeEligibleLeave.length > 0) {
                        await organizationDbConnection(ehrTables.auditEmpEligibleLeave).insert(auditEmployeeEligibleLeave).transacting(trx);
                    }
            
                    if (eligibleLeaveIds && eligibleLeaveIds.length > 0) {
                        await organizationDbConnection(ehrTables.empEligbleLeave).whereIn('Eligible_Leave_Id', eligibleLeaveIds).del().transacting(trx);
                    }
                }
                
                await organizationDbConnection(ehrTables.empEligbleLeave).insert(newEligibleLeaveDetails).transacting(trx);
            });
        }
        return 1;
    } catch (error) {
        console.error('Error in insertEligibleLeaveDetails function main catch block', error);
        return 0;
    }
}

//Function to get the leave balance details for the employees and leave types
async function getEmployeeEligibleLeaveDetails(organizationDbConnection,employeeIdArray,leaveTypeIdArray)  {
    try {
        //Get employee eligible leave details
        let employeeEligibleLeaveResult = await organizationDbConnection.select('EEL.*')
            .from(ehrTables.empEligbleLeave+' as EEL')
            .whereIn('EEL.Employee_Id', employeeIdArray)
            .whereIn('EEL.LeaveType_Id', leaveTypeIdArray)
            .then((result)=>{
                return (result && result.length) ? result : null;
            });

        return employeeEligibleLeaveResult;
    } catch (error) {
        console.log('Error in the getEmployeeEligibleLeaveDetails function main catch block.',error);
        throw error;
    }
}
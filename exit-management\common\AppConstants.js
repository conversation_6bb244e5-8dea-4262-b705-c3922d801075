const { supportEmailIds } = require("@cksiva09/hrapp-corelib/src/AppConstants")

const status = {
  statusApplied: 'Applied',
  statusRejected: 'Rejected',
  statusApproved: 'Approved',
  statusWaitingForApproval: 'Waiting for Approval',
  statusCompleted: 'Completed',
  statusSubmitted: 'Submitted',
  statusDraft: 'Draft',
  statusIncomplete: 'Incomplete',
  statusDeleted: 'Deleted'
}

const formNames = {
  formSuperAdmin: 'Super Admin',
  formDynamicFormBuilder: 'Dynamic Form Builder',
  formResignation: 'Exit Management',
  formServiceProviderAdmin : 'Service Provider Admin',
  recruitmentRequest:'Recruitment request',
  newPosition:'New Position',
  travelRequest:'Travel Request',
  compOff:'Compensatory Off Request'
}

const accessRights = {
  optionaChoiceAccess:'Role_Optional_Choice'
}

const formIds = {
  formIdResignation: 34,
  esicReasonId: 19,
  superAdmin: 147
}

const dateFormats = {
  YYYYMMDD: 'YYYY-MM-DD',
  YYYYMMDDHHmmss: 'YYYY-MM-DD HH:mm:ss'
}

const aws = {
  awsImageBucket: 'imageBucket'
}

const exitManagement = {
  resignationWorkflowModuleId: 2
}

const defaultValues = {
  defaultEsicReasonId: 3,
  replenishmentLeaveClosureBasedOn: ['Limited Replenishment on Approval','Unlimited Replenishment on Approval']
}

const awsSESTemplates = {
  withdrawnEmailToManager: 'withdrawnEmailToManager',
  cancelEmailToEmployee: 'cancelEmailToEmployee'
}

const redirectionUrl = {
  resignationRedirectionPath: '/employees/resignation'
}

const s3ImagePath = {
  withdrawnImagePath: '/Email-Template-Images/withdrawnImage.png',
  cancelImagePath: '/Email-Template-Images/cancelImage.png',
}

const syntrumValues = {
  integrationType: 'Syntrum',
  syncDirection: 'Push',
}


module.exports = {
  ...status,
  ...dateFormats,
  ...formNames,
  ...formIds,
  ...aws,
  ...exitManagement,
  ...accessRights,
  ...defaultValues,
  ...awsSESTemplates,
  ...redirectionUrl,
  ...s3ImagePath,
  supportEmailIds,
  syntrumValues
}

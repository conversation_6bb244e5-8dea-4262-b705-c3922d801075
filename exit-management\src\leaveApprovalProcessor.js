//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');
const moment = require('moment');
const appConstant = require("../common/AppConstants");

async function updateLeaveBalance(organizationDbConnection,leaveApprovalInputs){
    console.log("Inside updateLeaveBalance function",leaveApprovalInputs);
    try{
        let { leaveIds, leaveAction, orgCode, currentDateTime,completedBy } = leaveApprovalInputs;
        let allLeaveAction = ['approve','reject'];
        
        //If the inputs are not valid
        if(leaveIds?.length == 0 || !allLeaveAction.includes(leaveAction)){
            console.log('Invalid inputs in updateLeaveBalance function',leaveApprovalInputs);
            return { error: 'Invalid inputs'};
        }

        let leaveRequestDetails = await viewLeaveRequest(organizationDbConnection,leaveIds);
        let leaveDetailsGroupByUId = await commonLib.func.organizeData(leaveRequestDetails,'Leave_Id');
        console.log("leaveRequestDetails",leaveRequestDetails)
        let allEmployeeCurrentLeaveBalanceDetails = [];
        let updatedLeaveIds = [];
        let allEmployeesSelectedDetails = [];
        let leaveReplenishmentLeaveIds = [];
        let syntrumLeaveTypeIds = [];
        let leaveReplenishmentEmployeeIds = [];
        let leaveReplenishmentLeaveTypeIds = [];
        let rejectedOldApprovalStatus = ['Applied','Cancel Applied'];
        let alreadyUpdatedStatus = ['Approved','Cancelled','Rejected'];
        let syntrumApproveLeaveIdsByType = {};
        let syntrumCancelLeaveIdsByType = {};
        let approvalInputsResponse = {};

        return (
        organizationDbConnection
        .transaction(async function(trx) {
            for (const leaveId of leaveIds) {
                let triggerEarlyCheckout = 0;

                let employeeLeaveRequestDetails = leaveDetailsGroupByUId[leaveId]?.[0] ?? null;
                
                if(!employeeLeaveRequestDetails){
                    console.log('Employee leave details not exist',leaveId);
                    continue;
                }
               
                let {Approval_Status:leaveApprovalStatus,Employee_Id: employeeId, LeaveType_Id: leaveTypeId, Leave_Closure_Based_On} = employeeLeaveRequestDetails;
                
                if(alreadyUpdatedStatus.includes(leaveApprovalStatus)){
                    console.log('Leave status already updated',leaveId,employeeLeaveRequestDetails);
                    continue;
                }

                let inputToUpdateBalance = {
                    leaveId,
                    employeeLeaveRequestDetails,
                    leaveAction,
                    allEmployeeCurrentLeaveBalanceDetails,
                    updatedLeaveIds,
                    trx
                };

                let leaveApproveResponse = '';
                //Update from applied to approved status
                if(leaveAction == 'approve' && leaveApprovalStatus == 'Applied'){
                    leaveApproveResponse = await approveLeaveRequest(organizationDbConnection,inputToUpdateBalance);
                   
                    if(leaveApproveResponse.balanceUpdated){
                        console.log("approve-leaveApproveResponse",leaveApproveResponse);
                        allEmployeeCurrentLeaveBalanceDetails = leaveApproveResponse.currentLeaveBalanceDetailsResult;
                        updatedLeaveIds = leaveApproveResponse.updatedLeaveIdsResult;

                        if(appConstant.replenishmentLeaveClosureBasedOn.includes(Leave_Closure_Based_On)){
                            leaveReplenishmentLeaveIds.push(leaveId);
                            leaveReplenishmentEmployeeIds.push(employeeId);
                            leaveReplenishmentLeaveTypeIds.push(leaveTypeId);
                        }

                        syntrumLeaveTypeIds.push(leaveTypeId);
                        syntrumApproveLeaveIdsByType = await formSyntrumValidationInputs(syntrumApproveLeaveIdsByType,employeeLeaveRequestDetails);
                    }
                }else if(leaveAction == 'approve' && leaveApprovalStatus == 'Cancel Applied'){
                    leaveApproveResponse = await commonLib.employees.cancelLeaveRequest(organizationDbConnection,inputToUpdateBalance);
                   
                    if(leaveApproveResponse.balanceUpdated){
                        console.log("cancel-leaveApproveResponse",leaveApproveResponse);
                        allEmployeeCurrentLeaveBalanceDetails = leaveApproveResponse.currentLeaveBalanceDetailsResult;
                        updatedLeaveIds = leaveApproveResponse.updatedLeaveIdsResult;

                        triggerEarlyCheckout=1;
                        syntrumLeaveTypeIds.push(leaveTypeId);
                        syntrumCancelLeaveIdsByType = await formSyntrumValidationInputs(syntrumCancelLeaveIdsByType,employeeLeaveRequestDetails);
                    }
                }else if(leaveAction == 'reject' && rejectedOldApprovalStatus.includes(leaveApprovalStatus)){
                    updatedLeaveIds.push(leaveId);
                    triggerEarlyCheckout= 1;
                }else{
                    console.log('Invalid approval status');
                }

                if(triggerEarlyCheckout){
                    let summaryLeaveDetail = await pushSummaryLeaveDetails(employeeLeaveRequestDetails);
                    allEmployeesSelectedDetails.push(...summaryLeaveDetail);
                }
                console.log('updateLeaveBalance Response - employeeLeaveRequestDetails',employeeLeaveRequestDetails,'leaveApproveResponse: ', leaveApproveResponse)
            };

            if(updatedLeaveIds?.length) {
                let updateLeaveStatusInputs = { 
                    leaveIds: updatedLeaveIds, 
                    leaveAction 
                };
                let isStatusUpdated = await updateApprovalStatusForLeaves(organizationDbConnection, trx, updateLeaveStatusInputs);
                
                if(isStatusUpdated){
                    let syntrumInputs = { syntrumApproveLeaveIdsByType, syntrumCancelLeaveIdsByType, updatedLeaveIds, orgCode, currentDateTime,syntrumLeaveTypeIds };
                    let summaryInputs = { summaryDetails: allEmployeesSelectedDetails, currentDateTime, orgCode,leaveIds: updatedLeaveIds};
                    let replenishmentInputs = { 
                        eligibleLeaveIds: leaveReplenishmentLeaveIds,
                        leaveReplenishmentEmployeeIds,
                        leaveReplenishmentLeaveTypeIds, 
                        currentLeaveBalanceDetails:allEmployeeCurrentLeaveBalanceDetails,
                        leaveDetailsGroupByUId,
                        leaveRequestDetails 
                    };
                    console.log("replenishmentInputs",replenishmentInputs,allEmployeeCurrentLeaveBalanceDetails)
                    approvalInputsResponse = { syntrumInputs,summaryInputs,replenishmentInputs };  
                    return { result: {'success': 1 , 'message': ''}, approvalInputsResponse, updatedLeaveIds};
                }else {
                    return { result: {'success': 1 , 'message': 'Leave status could not be updated. The record may be missing or already processed.'}, approvalInputsResponse:{},updatedLeaveIds: []};
                }
            }
            
            return {  result: { 'success': 0, 'message': 'An error occurred while attempting to update the status. This may be due to a missing record, a previously updated status, or a system error.'},approvalInputsResponse: {},updatedLeaveIds: [] };
        })
        .then(async(response) =>{
            if(response.approvalInputsResponse && Object.keys(response.approvalInputsResponse).length){
                let approvalInputsDetails = { orgCode, approvalInputs: response.approvalInputsResponse,leaveApprovalInputs};
                console.log("approvalInputsResponse-",response.approvalInputsResponse,approvalInputsDetails['approvalInputs']['replenishmentInputs']);
                //Create a system log for the status update
                let systemLogParams = {
                    action: leaveAction,
                    userIp: '',
                    employeeId: completedBy || 0,
                    formName: 'Leave',
                    trackingColumn: '',
                    uniqueId: response.updatedLeaveIds[0],
                    changedData: JSON.stringify(response.updatedLeaveIds),
                    organizationDbConnection: organizationDbConnection
                };
                await Promise.all([
                    commonLib.func.createSystemLogActivities(systemLogParams),
                    commonLib.func.triggerLambda('handle-leave-approval-orchestrator','Event',approvalInputsDetails,1)
                ]);
            }
            return response.result;
        })
        .catch(catchError=>{
            console.log('Error in updateLeaveBalance function catch block.', catchError);
            return { 'success': 0, 'message': 'An error occurred while attempting to update the status. This may be due to a missing record, a previously updated status, or a system error.'};
        }))
    }catch(error){
        console.error('Error in updateLeaveBalance function main catch block.', error);
        return { 'success': 0, 'message': 'An error occurred while attempting to update the status.'};
    }
}

async function viewLeaveRequest(organizationDbConnection,leaveIds) {
    try{
        return (await organizationDbConnection.select([
                'L.Leave_Id',
                'L.Employee_Id',
                'L.Approver_Id',
                'L.Added_By',
                'L.LeaveType_Id',
                'L.Total_Days as Leave_Request_Days',
                'L.Start_Date',
                'L.End_Date',
                'L.Approval_Status',
                'L.Duration',
                'LT.Leave_Type',
                'LT.Approve_Leave_Type_Code',
                'LT.Cancel_Leave_Type_Code',
                'EEL.Eligible_Days',
                'EEL.Leaves_Taken',
                'EEL.No_Of_Days',
                'EEL.Last_CO_Balance',
                'EEL.Leave_Balance',
                'LT.Carry_Over',
                'LT.Leave_Enforcement_Configuration',
                'LT.Leave_Closure_Based_On',
                'LT.Total_Days',
                'LT.Replenishment_Limit as leaveTypeReplenishmentLimit',
                'EEL.Replenishment_Limit as employeeReplenishmentLimit',
                organizationDbConnection.raw('SUM(EN.Encashed_Days) AS encashedDays')
            ])
            .from({ L: ehrTables.empLeaves })
            .leftJoin({ LT: ehrTables.leaveTypes }, 'L.LeaveType_Id', 'LT.LeaveType_Id')
            .innerJoin({ EEL: ehrTables.empEligbleLeave },  function() {
                this.on("L.Employee_Id", "=", "EEL.Employee_Id")
                    .andOn("L.LeaveType_Id", "=", "EEL.LeaveType_Id");
            })
            .leftJoin({ EN: ehrTables.encashedLeave },  function() {
                this.on("L.Employee_Id", "=", "EN.Employee_Id")
                    .andOn("L.LeaveType_Id", "=", "EN.LeaveType_Id")
                    .andOn("EEL.CO_Year", "=", "EN.EN_Year")
            })
            .orderBy('L.End_Date','DESC')
            .whereIn('L.Leave_Id', leaveIds));
    } catch (error) {
        console.error('Error in viewLeaveRequest function main catch block.', error);
        throw error;
    }
}
  
async function approveLeaveRequest(organizationDbConnection,inputToUpdateBalance){
    let { leaveId, employeeLeaveRequestDetails, allEmployeeCurrentLeaveBalanceDetails,trx, updatedLeaveIds } = inputToUpdateBalance;

    try {
        let leaveBalanceUpdateDetails = {};

        let { Carry_Over: carryOverLeave, Employee_Id: employeeId, LeaveType_Id: leaveTypeId, Leaves_Taken: leavesTaken, Last_CO_Balance: lastCoBalance, Leave_Balance: leaveBalance, Eligible_Days: currentYearEmployeeLeaveEligibility, Leave_Request_Days: leaveRequestDays } = employeeLeaveRequestDetails;

        let currentLeaveBalanceKey = `${employeeId}|${leaveTypeId}`;
        let currentLeaveBalanceDetails = allEmployeeCurrentLeaveBalanceDetails[currentLeaveBalanceKey] ?? null;

        // Check employee ID and Leave Type ID existence
        if (currentLeaveBalanceDetails && Object.keys(currentLeaveBalanceDetails).length > 0) {
            leavesTaken = currentLeaveBalanceDetails['Leaves_Taken'];
            lastCoBalance = currentLeaveBalanceDetails['Last_CO_Balance'];
            leaveBalance = currentLeaveBalanceDetails['Leave_Balance'];
        }

        // Update leaves taken and balance
        let leavesTakenToUpdate = leavesTaken + leaveRequestDays;

        let lastCoBalanceToUpdate;

        carryOverLeave = carryOverLeave.toLowerCase();
        //If carry over is enabled in the leave type
        if (carryOverLeave === 'yes') {
            if( leavesTakenToUpdate > currentYearEmployeeLeaveEligibility && lastCoBalance){
                // Update carryover balance if conditions are met
                if (leavesTaken > currentYearEmployeeLeaveEligibility) {
                    lastCoBalanceToUpdate = lastCoBalance - Math.min(leaveRequestDays, lastCoBalance);
                }else{
                    let currentYearLeaveBalance = leavesTakenToUpdate-currentYearEmployeeLeaveEligibility;
                    let carryOverBalance = Math.min(currentYearLeaveBalance, lastCoBalance);
                    lastCoBalanceToUpdate = lastCoBalance-carryOverBalance;
                }
            }else{
                console.log('carry over enabled but total leave taken is lesser than current year eligibility')
            }
        }else{
            lastCoBalanceToUpdate = lastCoBalance;//no change
        }

        leaveBalanceUpdateDetails.Leaves_Taken = leavesTakenToUpdate;
        leaveBalanceUpdateDetails.Last_CO_Balance = lastCoBalanceToUpdate;
        leaveBalanceUpdateDetails.Leave_Balance = leaveBalance - leaveRequestDays;
        
        let uniqueIdDetails = { employeeId, leaveTypeId };
        console.log("leaveBalanceUpdateDetails",leaveBalanceUpdateDetails,uniqueIdDetails);
        let isUpdated = await commonLib.employees.updateEligibleLeaveDetails(organizationDbConnection, trx, leaveBalanceUpdateDetails, uniqueIdDetails);
        if(isUpdated){
            allEmployeeCurrentLeaveBalanceDetails[currentLeaveBalanceKey] = {
                ...allEmployeeCurrentLeaveBalanceDetails[currentLeaveBalanceKey], // existing fields
                ...leaveBalanceUpdateDetails // new or updated fields
            };

            updatedLeaveIds.push(leaveId);
        }else{
            console.log('Leave balance is not updated in approveLeaveRequest function',leaveId);
        }
        return { currentLeaveBalanceDetailsResult: allEmployeeCurrentLeaveBalanceDetails, balanceUpdated: isUpdated, updatedLeaveIdsResult: updatedLeaveIds};
    } catch (error) {
        console.log('Error in the approveLeaveRequest function main catch block.',error, leaveId, employeeLeaveRequestDetails, allEmployeeCurrentLeaveBalanceDetails,updatedLeaveIds);
        return { currentLeaveBalanceDetailsResult: '', balanceUpdated: 0, updatedLeaveIdsResult: ''};
    }
}

async function pushSummaryLeaveDetails(employeeLeaveRequestDetails){
    let selectedDetails = [];
    try {
        let { Start_Date,End_Date,Employee_Id: employeeId,Leave_Id: leaveId }= employeeLeaveRequestDetails;
        let startDate = moment(Start_Date);
        let endDate = moment(End_Date);
        while (startDate.isSameOrBefore(endDate)) {
            selectedDetails.push({
                Unique_Id: leaveId,
                Employee_Id: employeeId,
                Summary_Date: startDate.format('YYYY-MM-DD')
            });
        
            startDate.add(1, 'day');
        }
        return selectedDetails;
    } catch (error) {
        console.log('Error in the pushSummaryLeaveDetails function main catch block.',error,selectedDetails);
        return selectedDetails;
    }
}

async function formSyntrumValidationInputs(syntrumLeaveTypeDetailsInput,employeeLeaveRequestDetails){
    console.log("Inside formSyntrumValidationInputs function",employeeLeaveRequestDetails)
    try{
        let { LeaveType_Id:leaveTypeId, Approve_Leave_Type_Code, Cancel_Leave_Type_Code, Leave_Id } = employeeLeaveRequestDetails;

        let syntrumLeaveTypeDetails = syntrumLeaveTypeDetailsInput;
        console.log("syntrumLeaveTypeDetails",syntrumLeaveTypeDetails)
        if (!syntrumLeaveTypeDetails[leaveTypeId]) {
            syntrumLeaveTypeDetails[leaveTypeId] = {
                leaveTypeId: leaveTypeId,
                leaveIds: [],
                approveLeaveTypeCode: Approve_Leave_Type_Code || '',
                cancelLeaveTypeCode: Cancel_Leave_Type_Code || ''
            };
        }

        syntrumLeaveTypeDetails[leaveTypeId].leaveIds.push(Leave_Id);
        return syntrumLeaveTypeDetails;
    }
    catch (error) {
        console.error('Error in formSyntrumValidationInputs function main catch block.', error);
        return syntrumLeaveTypeDetailsInput;
    }
}

/**
 * Updates the Approval_Status in emp_leaves table based on action and current status.
 * 
 * @param {Array<number>} leaveIds - Array of leave IDs to be updated.
 * @param {Object} knex - The initialized Knex instance.
 * @returns {Object} Result of the update operation.
*/
async function updateApprovalStatusForLeaves(organizationDbConnection, trx, updateLeaveStatusInputs) {
    try {
        const { leaveAction, leaveIds } = updateLeaveStatusInputs;
        let updateResult = await organizationDbConnection(ehrTables.empLeaves)
        .whereIn('Leave_Id', leaveIds)
        .update({
            Approval_Status: organizationDbConnection.raw(`
            CASE
                WHEN ? = 'approve' AND Approval_Status = 'Applied' THEN 'Approved'
                WHEN ? = 'approve' AND Approval_Status = 'Cancel Applied' THEN 'Cancelled'
                WHEN ? = 'reject' AND Approval_Status = 'Applied' THEN 'Rejected'
                WHEN ? = 'reject' AND Approval_Status = 'Cancel Applied' THEN 'Approved'
                ELSE Approval_Status
            END
            `, [leaveAction, leaveAction, leaveAction, leaveAction])
        })
        .transacting(trx);
    
        return updateResult;
    } catch (error) {
        console.error('Error in updateApprovalStatusForLeaves function main catch block', error);
        return 0;
    }
}

async function validateAndTriggerAttendanceSummary(organizationDbConnection,otherDetails){
    try {
        console.log('Inside validateAndTriggerAttendanceSummary function',otherDetails)
        let leaveIds = 0;
        let { summaryDetails, orgCode, currentDateTime,tableName} = otherDetails;
        if(summaryDetails && summaryDetails.length > 0){
            //Validate if the short time off request is created for the early checkout
            if(tableName == 'short_time_off' || tableName == 'emp_leaves'){
                let uniqueIds = [];
                let formNameToLog = '';
                let uniqueColumnName;
                if(tableName == 'short_time_off'){
                    let shortTimeOffIds = await organizationDbConnection(tableName)
                                            .pluck('Short_Time_Off_Id')
                                            .where('Employee_Id',summaryDetails[0].Employee_Id)
                                            .where('Short_Time_Off_Date',summaryDetails[0].Summary_Date);
                    uniqueIds = shortTimeOffIds ?? null;
                    uniqueColumnName = 'Early_Checkout_Short_Time_Off_Id';
                    formNameToLog = 'short time off';
                }else{
                    leaveIds = otherDetails.leaveIds;
                    uniqueIds = leaveIds;
                    uniqueColumnName = 'Early_Checkout_Leave_Id';
                    formNameToLog = 'leave';
                }
                if(uniqueIds?.length){
                    let attendanceSummaryRows = await organizationDbConnection('employee_attendance_summary')
                                            .select('Attendance_Summary_Id',uniqueColumnName)
                                            .whereIn(uniqueColumnName,uniqueIds);
                    if(attendanceSummaryRows?.length){
                        const attendanceSummaryId = attendanceSummaryRows.map(r => r.Attendance_Summary_Id);
                        const existingUniqueIds = attendanceSummaryRows.map(r => r[uniqueColumnName]);

                        console.log('Attendance Summary IDs:', attendanceSummaryId);
                        console.log('Existing Unique IDs:', existingUniqueIds);

                        //Leaves or short time off that are not created for early checkout. So trigger the attendance summary process for these ids
                        const missingUniqueIds = uniqueIds.filter(id => !existingUniqueIds.includes(id));
                        console.log('Missing Unique IDs:', missingUniqueIds);

                        //If the attendance summary id exists then short time off is created for the early checkout
                        if(attendanceSummaryId.length > 0){
                            let updateEarlyCheckoutIgnoreStatusParams = {
                                'Early_Checkout_Ignore_Status': 'Yes',
                                'Updated_On': currentDateTime
                            };
                        
                            //Update the early checkout ignore status to yes and also attendance summary process should not be triggered
                            let updateEarlyCheckoutIgnoreStatusResult = await organizationDbConnection('employee_attendance_summary')
                            .update(updateEarlyCheckoutIgnoreStatusParams)
                            .whereIn('Attendance_Summary_Id',attendanceSummaryId);

                            if(updateEarlyCheckoutIgnoreStatusResult > 0){
                                //Create a system log for the early checkout ignore status
                                let systemLogParams = {
                                    action: 'Ignore',
                                    userIp: '',
                                    employeeId: '',
                                    formName: 'Early Checkout',
                                    trackingColumn: '',
                                    message: `Early checkout is ignored the as the ${formNameToLog} during the approval - ${attendanceSummaryId.join(',')}`,
                                    organizationDbConnection: organizationDbConnection
                                };
                        
                                await commonLib.func.createSystemLogActivities(systemLogParams);
                            }else{
                                console.log('Record is not ignored',attendanceSummaryId,updateEarlyCheckoutIgnoreStatusParams);
                            }
                        }

                        if(missingUniqueIds?.length){
                            const matchedSummaryDetails = summaryDetails.filter(item => missingUniqueIds.includes(item.Unique_Id));
                            console.log('The request is triggered from early checkout',attendanceSummaryRows,matchedSummaryDetails,missingUniqueIds,uniqueIds);
                            await triggerAttendanceSummaryProcess(orgCode, matchedSummaryDetails);
                        }
                    }else{
                        console.log('The request is not created for the early checkout',attendanceSummaryRows,uniqueIds);
                        await triggerAttendanceSummaryProcess(orgCode, summaryDetails);
                    }
                }else{
                    console.log(`Empty unique ids`,uniqueIds);
                    await triggerAttendanceSummaryProcess(orgCode, summaryDetails);
                }
            }else{
                console.log('Input Table name not matches. Trigger attendance summary');
                await triggerAttendanceSummaryProcess(orgCode, summaryDetails);
            }
        }else{
            console.log('Attendance summary step function cannot be triggered as the summary details are not present');
        }
        return 1;
    }
    catch (error) {
        console.log('Error in updateTableBasedOnConditionSync function main catch block', error);
        return 0;
    }
}

//Function to trigger the attendance summary step function
async function triggerAttendanceSummaryProcess(orgCode, selectedDetails){
    console.log('Inside triggerAttendanceSummaryProcess function',orgCode,selectedDetails);
    return new Promise(async function(resolve, reject) {
        try {
            //Initiating the background process to add, update, or delete attendance summary records.
            let inputParams = {
                orgCode: orgCode,
                employeeAttendanceDetails: JSON.stringify(selectedDetails),
                context: ''
            }
            console.log('triggerAttendanceSummaryProcess , Input params', inputParams);
            let triggerStepFunctionResponse = await commonLib.stepFunctions.triggerStepFunction(process.env.attendanceSummaryProcess, 'manageAttendanceSummary', '', inputParams); 
            if(!triggerStepFunctionResponse){
                console.log('Error in triggering attendance summary step function', triggerStepFunctionResponse);
            }
            resolve("success");
        } catch(mainError) {
            console.log('Error in triggerAttendanceSummaryProcess function main catch block', mainError);
            resolve("success");
        }
    })
}

module.exports={
    updateLeaveBalance,
    validateAndTriggerAttendanceSummary,
    triggerAttendanceSummaryProcess
};
{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "secretName": "prod/hrapp/pgaccess", "firebaseAuthorizer": "arn:aws:lambda:ap-south-1:484056187456:function:ATS-prod-firebaseauthorizer", "role": "arn:aws:iam::484056187456:role/LambdaMicroservice", "dbPrefix": "hrapp_", "hrappProfileBucket": "s3.images.hrapp.co", "domainName": "hrapp", "workflowEngineUrl": "https://api.hrapp.co/workflowEngine", "customDomainName": "api.hrapp.co", "logoBucket": "s3.logos.hrapp.co", "emailFrom": "<EMAIL>", "sesRegion": "us-west-2", "region": "ap-south-1", "webAddress": ".co", "leaveStatusDomainName": "hrapp.co", "camuExitStaffEndPoint": "external/staff/exit", "documentsBucket": "s3.taxdocs.hrapp.co", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-processAirTicketSummary", "attendanceSummaryProcess": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-attendanceSummaryStepFunction", "syntrumLeaveIntegrationProcess": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-pushLeaveToSyntrum"}
{"securityGroupIds": ["sg-098a0324de5632c9e", "sg-03b2b753038f67293"], "subnetIds": ["subnet-020dff85cddc0752e", "subnet-0b1a6fbc2c7b782ad"], "secretName": "hrapp-stage", "firebaseAuthorizer": "arn:aws:lambda:ap-south-1:692647644057:function:ATS-dev-firebaseauthorizer", "role": "arn:aws:iam::692647644057:role/lambdaFullAccess", "dbPrefix": "hrapp_", "hrappProfileBucket": "s3.hrapp-dev-public-asset", "domainName": "hrapp", "workflowEngineUrl": "https://api.hrapp.co.in/workflowEngine", "customDomainName": "api.hrapp.co.in", "logoBucket": "s3.hrapp-dev-public-images", "emailFrom": "<EMAIL>", "documentsBucket": "caprice-dev-stage", "sesRegion": "us-east-1", "region": "ap-south-1", "webAddress": ".co.in", "leaveStatusDomainName": "hrapp.co.in", "camuExitStaffEndPoint": "external/staff/exit", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-processAirTicketSummary", "attendanceSummaryProcess": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-attendanceSummaryStepFunction", "syntrumLeaveIntegrationProcess": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-pushLeaveToSyntrum"}
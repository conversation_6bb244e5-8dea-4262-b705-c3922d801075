scalar Date
type Query {
  getAllDesignation(envelope: Envelope!): MultipleDesignationResponse!
  getAllDepartment(envelope: Envelope!): MultipleDepartmentResponse!
  getAllEmployeeType(envelope: Envelope!): MultipleEmployeeTypeResponse!
  getAllLocation(envelope: Envelope!): MultipleLocationResponse!
  getAllEmployees(
    envelope: Envelope!
    filter: EmployeeFilter!
  ): MultipleEmployeeResponse!
  getEmployeeResignation(
    envelope: Envelope!
    employeeId: Int
    resignationId: Int!
  ): ResignationResponse!
  getEmployeeCurrentResignation(
    envelope: Envelope!
    employeeId: Int
  ): EmployeeCurrentResignationResponse!
  getAllResignation(
    envelope: Envelope!
    filter: Filter!
  ): MultipleResignationResponse!
  getResignationStatus(
    envelope: Envelope!
    resignationId: Int
  ): ResignationStatusResponse!
  getWorkflowTaskDynamicFormDetails(
    envelope: Envelope!
    dynamicFormId: Int!
    workflowTaskId: String!
  ): WorkflowTaskDynamicFormResponse!
  getEmployeeNotificationPeriodDays(
    envelope: Envelope!
    employeeId: Int!
  ): NoticePeriodDaysResponse!
}

type Mutation {
  createResignation(
    envelope: Envelope!
    employeeId: Int
    resignationDate: Date!
    appliedDate: Date!
    reasonId: Int!
    fileName: String
    esicReason : String
    relievingReasonComment: String
  ): CreateResignationResponse!
  updateResignationDate(
    envelope: Envelope!
    resignationId: Int!
      fileName: String
    resignationDate: Date!
    appliedDate: Date!
  ): Response!
  updateDynamicFormDetail(
    envelope: Envelope!
    responseId: Int
    taskId: String!
    formData: String!
    formStatus: String!
    resignationId: Int!
    workflowInstanceId: String
  ): Response!
  updateResignationReason(
    envelope: Envelope!
    resignationId: Int!
    reasonId: Int!
    relievingReasonComment: String,
    esicReason : String
  ): Response!
  withdrawnCancelResignation(
    envelope: Envelope!
    resignationId: Int!
    approvalStatus: String!
    comment: String!
  ): Response!
}

input Envelope {
  orgCode: String!
  loggedInUserId: Int!
  formId: Int
}

input Filter {
  limit: Int!
  offset: Int!
  searchValue: String
  status: [String]
  employees: [Int]
  noticeDate: DateFilter
  resignationDate: DateFilter
  designationId: Int
  departmentId: Int
}

input EmployeeFilter {
  searchValue: String
  status: [String]
  designationId: Int
  departmentId: Int
  employeeTypeId: Int
  locationId: Int
}

input DateFilter {
  start: String
  end: String
}

type CreateResignationResponse {
  error: Error
  result: CreateResignationResult
}

type ResignationResponse {
  error: Error
  result: Resignation
  relievingReasonDetails: [relievingReasonResponse]
}

type EmployeeCurrentResignationResponse {
  error: Error
  result: EmployeeCurrentResignationResult
  relievingReasonDetails: [relievingReasonResponse]
}

type WorkflowTaskDynamicFormResponse {
  error: Error
  result: WorkflowTaskDynamicFormResult
}

type MultipleResignationResponse {
  error: Error
  result: [Resignation]
}

type MultipleEmployeeResponse {
  error: Error
  result: [Employee]
  relievingReasonDetails: [relievingReasonResponse]
}

type ResignationStatusResponse {
  error: Error
  result: ResignationStatus
}

type NoticePeriodDaysResponse {
  error: Error
  result: Int
}

type Response {
  error: Error
  result: Result
  extendedErrors: [String]
}

type Error {
  code: String
  message: String
}

type Result {
  success: Boolean
  message: String
  data: String
  validation: [Validation]
}

type ResignationStatus {
  status: String
  workflowInstanceId: String
  appliedDate: String
  resignationDate: String
}

type CreateResignationResult {
  success: Boolean
  message: String
  validation: [Validation]
  resignation: CreatedResignation
  dynamicFormTemplate: DynamicFormTemplate
  workflow: Workflow
}

type EmployeeCurrentResignationResult {
  resignation: Resignation
  dynamicFormTemplate: DynamicFormTemplate
  dynamicFormResponse: DynamicFormResponse
  workflow: Workflow
}

type WorkflowTaskDynamicFormResult {
  dynamicFormTemplates: DynamicFormTemplate
  dynamicFormResponse: DynamicFormResponse
}

type DynamicFormTemplate {
  templateId: Int
  templateName: String
  template: String
  conversational: Int
}

type DynamicFormResponse {
  formStatus: String
  formResponseId: Int
  formResponse: String
  addedUserId: Int
  addedUserName: String
  addedOn: String
  updatedUserId: Int
  updatedUserName: String
  updatedOn: String
}

type Workflow {
  workflowInstanceId: String
  workflowTaskId: String
}

type CreatedResignation {
  employeeId: Int
  resignationId: Int
  resignationDate: String
  appliedDate: String
  resignationStatus: String
}

type Validation {
  code: String
  message: String
}

type Resignation {
  resignationId: Int
  employeeId: Int
  userDefinedEmpId: String
  employeeIdWithPrefix: String
  empStatus: String
  employeeName: String
  employeeGender: String
  employeePhotoPath: String
  employeeIsManager: Int
  employeeDesignationId: Int
  employeeDesignationName: String
  employeeDepartmentId: Int
  employeeDepartmentName: String
  approverId: Int
  fileName: String
  approverName: String
  resignationStatus: String
  appliedDate: Date
  resignationDate: Date
  workflowInstanceId: String
  workflowStatus: String
  addedOn: Date
  addedUserId: Int
  addedUserName: String
  updatedOn: Date
  updatedUserId: Int
  updatedUserName: String
  reasonId: Int
  esicReasonName: String
  relievingReasonComment: String
  withdrawnCancellationComment: String
  isPayslipGenerated: Int
   locationId:Int
  locationName:String
  empTypeId:Int
  employeeType:String
}

type MultipleDesignationResponse {
  error: Error
  result: [Designation]
}

type Designation {
  designationId: Int
  designationName: String
}

type MultipleDepartmentResponse {
  error: Error
  result: [Department]
}

type MultipleEmployeeTypeResponse {
  error: Error
  result: [EmployeeType]
}

type MultipleLocationResponse {
  error: Error
  result: [Location]
}

type Location {
  locationId: Int
  locationName: String
}

type Department {
  departmentId: Int
  departmentName: String
}

type EmployeeType {
  employeeTypeId: Int
  employeeType: String
}

type Employee {
  id: Int
  idPrefix: String
  name: String
  photoPath: String
  designationId: Int
  designationName: String
  departmentId: Int
  departmentName: String
  isManager: Int
  gender: String
  status: String
  locationId:Int
  locationName:String
  empTypeId:Int
  employeeType:String
}

type relievingReasonResponse {
  reasonId: Int
  esicReasonName: String
}

schema {
  query: Query
  mutation: Mutation
}
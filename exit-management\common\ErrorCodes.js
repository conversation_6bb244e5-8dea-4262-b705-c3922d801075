// defined error codes & its descriped error message
const COMMON_DB_CODES = {
  ECONNREFUSED: {
    code: '_DB0000',
    message: 'Database connection problem'
  },
  _DB0001: {
    code: '_DB0001',
    message: 'Error while retrieving the employee access rights'
  },
  _DB0002: {
    code: '_DB0002',
    message: 'Error while try to check the employee access rights'
  },
  _DB0003: {
    code: '_DB0003',
    message: 'Error while getting user timezone'
  },
  _DB0004: {
    code: '_DB0004',
    message: 'Error while retrieving all designations for drop down'
  },
  _DB0005: {
    code: '_DB0005',
    message: 'Error while retrieving all departments for drop down'
  },
  _DB0006: {
    code: '_DB0006',
    message: 'Error while retrieving all employee type list for search'
  },
  _DB0007: {
    code: '_DB0007',
    message: 'Error while retrieving all locations list for search'
  }
};

const COMMON_LOGICAL_CODES = {
  _DB0100: {
    code: '_DB0100',
    message: 'This employee does not have a view access rights'
  },
  _DB0101: {
    code: '_DB0101',
    message: 'This employee does not have a create access rights'
  },
  _DB0102: {
    code: '_DB0102',
    message: 'This employee does not have a edit access rights'
  },
  _DB0103: {
    code: '_DB0103',
    message: 'This employee does not have a delete access rights'
  },
};

const UNHANDLED_CODES = {
  _UH0001: {
    code: '_UH0001',
    message: 'Something went wrong! Not handled'
  }
};

const EMPLOYEE_RESIGNATION_DB_CODES = {
  ERE0001: {
    code: 'ERE0001',
    message: 'Error while create the resignation details'
  },
  ERE0002: {
    code: 'ERE0002',
    message: `Error while update the resignation date`
  },
  ERE0003: {
    code: 'ERE0003',
    message: `Error while retrieving the resignation list details`
  },
  ERE0004: {
    code: 'ERE0004',
    message: `Error while retrieving the resignation details`
  },
  ERE0005: {
    code: 'ERE0005',
    message: `Error while update dynamic form response details`
  },
  ERE0006: {
    code: 'ERE0006',
    message: `Error while create dynamic form response details`
  },
  ERE0007: {
    code: 'ERE0007',
    message: `Error while retrieving the resignation status`
  },
  ERE0008: {
    code: 'ERE0008',
    message: `Error while retrieving the resignation related dynamic form template detail`
  },
  ERE0009: {
    code: 'ERE0009',
    message: `Error while retrieving the resignation related dynamic form response detail`
  },
  ERE0010: {
    code: 'ERE0010',
    message: `Error while initiate workflow for new resignation`
  },
  ERE0011: {
    code: 'ERE0011',
    message: 'Error while retrieving all employees list for search'
  },
  ERE0012: {
    code: 'ERE0012',
    message: 'Error while retrieving the employee notice period days'
  },
  ERE0013: {
    code: 'ERE0013',
    message: 'Error while updating the resignation withdrawn or cancelation details.'
  },
  ERE0014: {
    code: 'ERE0014',
    message: 'Error while retrieving the employee details.'
  },
  ERE0015: {
    code: 'ERE0015',
    message: 'Invalid resignation id.'
  },
  ERE0016: {
    code: 'ERE0016',
    message: 'Resignation reason cannot be updated as the resignation is already withdrawn or canceled or rejected.'
  },
  ERE0017: {
    code: 'ERE0017',
    message: 'Resignation reason is not updated as the resignation record is opened or already updated in the same or some other user session.'
  },
  ERE0018: {
    code: 'ERE0018',
    message: 'Resignation status is not updated as the resignation record is opened or already updated in the same or some other user session.'
  },
  ERE0019: {
    code: 'ERE0019',
    message: 'Error while updating the resignation reason.'
  }
};

const EMPLOYEE_RESIGNATION_LOGICAL_CODES = {
  ERE0101: {
    code: 'ERE0101',
    message: 'Error while process the request for create the resignation details'
  },
  ERE0102: {
    code: 'ERE0102',
    message: `Error while process the request for update the resignation date`
  },
  ERE0103: {
    code: 'ERE0103',
    message: `Error while process the request for retrieve the resignation list details`
  },
  ERE0104: {
    code: 'ERE0104',
    message: `Error while process the request for retrieving the resignation details`
  },
  ERE0105: {
    code: 'ERE0105',
    message: `Error while process the request for create or update dynamic form response details`
  },
  ERE0106: {
    code: 'ERE0106',
    message: `Error while process the request for retrieving the resignation status`
  },
  ERE0107: {
    code: 'ERE0107',
    message: 'Error while process the request for retrieving all employees list for search'
  },
  ERE0108: {
    code: 'ERE0108',
    message: 'Resignation date is required'
  },
  ERE0109: {
    code: 'ERE0109',
    message: 'Applied date is required'
  },
  ERE0110: {
    code: 'ERE0110',
    message: 'Resignation date was lesser than applied date'
  },
  ERE0111: {
    code: 'ERE0111',
    message: 'Invalid Date'
  },
  ERE0112:{
    code: 'ERE0112',
    message: 'Resignation record already exists.'
  },
  ERE0113: {
    code: 'ERE0113',
    message: 'Could not able to edit the Approved resignation record.'
  },
  ERE0114: {
    code: 'ERE0114',
    message: 'Resignation record does not exist.'
  },
  ERE0115: {
    code: 'ERE0115',
    message1: 'Comment is required.',
    message2: 'Only alphanumeric, dot, and spaces allowed.',
    message3: 'Please enter at least 5 characters and not more than 600 characters.'
  },
  ERE0117: {
    code: 'ERE0117',
    message: `Error while processing the request to update the resignation reason.`
  },
  ERE0118: {
    code: 'ERE0118',
    message: `The resignation record was already withdrawn or canceled or rejected.`
  },
  ERE0119: {
    code: 'ERE0119',
    message: `Resignation cannot be withdrawn or canceled as the resignation date is less than the current date.`
  },
  ERE0120: {
    code: 'ERE0120',
    message: `Error while processing the request to withdrawn or cancel the resignation.`
  },
  ERE0121: {
    code: 'ERE0121',
    message: `Invalid approval status.`
  },
  ERE0122: {
    code: 'ERE0122',
    message: `Resignation withdrawn or canceled but unable to send email.`
  },
  ERE0123: {
    code: 'ERE0123',
    message: `Resignation withdrawn but unable to send email to the reporting manager as the email address is not configured.`
  },
  ERE0124: {
    code: 'ERE0124',
    message: `Resignation canceled but unable to send email to the employee as the email address is not configured.`
  },
  ERE0126: {
    code: 'ERE0126',
    message: 'Error while getting the manager assigned for the employee'
  },
  ERE0127: {
    code: 'ERE0127',
    message: 'Please complete the workflow configuration to apply/initiate a resignation'
  },
  ERE0128: {
    code: 'ERE0128',
    message: 'Please assign a manager for the employee to apply/initiate a resignation'
  },
  ERE0129: {
    code: 'ERE0129',
    message: `Resignation date cannot be updated as the employee full and final settlement is initiated.`
  },
  ERE0130: {
    code: 'ERE0130',
    message: `Resignation cannot be withdrawn or canceled as the employee full and final settlement is initiated.`
  },
  ESS0136:{
    code: 'ESS0136',
    message: 'Bank account number is already associated with another employee. Please verify the account number.'
  },
  ESS0137:{
    code: 'ESS0137',
    message: 'Active bank for the employee already exists.'
  },
  ESS0138:{
    code: 'ESS0138',
    message: 'Employee ID is used for another employee record. Please update a unique employee ID and save again.'
  },
  ESS0139:{
    code: 'ESS0139',
    message: 'Mobile No already exists for an active employee. Please verify the mobile number.'
  },
  ESS0140:{
    code: 'ESS0140',
    message: 'Employee email already exists for an active employee. Please verify the email.'
  },
  ESS0126: {
    code: 'ESS0126',
    message: 'Duplicate active employee detail found.'
  },
};

const errorCodes = {
  ...COMMON_DB_CODES,
  ...COMMON_LOGICAL_CODES,
  ...UNHANDLED_CODES,
  ...EMPLOYEE_RESIGNATION_LOGICAL_CODES,
  ...EMPLOYEE_RESIGNATION_DB_CODES
};

module.exports = {
  ...errorCodes
};

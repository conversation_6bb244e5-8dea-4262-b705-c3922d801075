const Helper = require('./helper');
const Common = require('../common');
const { updateStatusAndAssetMapping }=require('../common/CommonFunctions');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const appConstant = require("../common/AppConstants");
const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');
const { updateWorkflowResignationInstanceData, updateWorkflowLeaveInstanceData, updatePreApprovalRequestInstanceData } = require('../common/CommonFunctions');
const { updateLeaveBalance,validateAndTriggerAttendanceSummary } = require('./leaveApprovalProcessor');

module.exports.getDbProperties = async (event, context) => {
    let orgCode = JSON.parse(event.body).orgCode;
    let connection = orgCode ? await commonLib.func.getDataBaseConnection(
        {
            stageName: process.env.stageName, dbPrefix: process.env.dbPrefix, dbSecretName: process.env.secretName,
            region: process.env.region, orgCode: orgCode, secretRequired: 1
        }) : null;
    let result = connection && connection.OrganizationDb  ? connection.OrganizationDb : {};
    
    result.connection.port = "3306";
    
    const response = {
        statusCode: 200,
        body: JSON.stringify({
            result: result
        })
    }
    return response
}
//Resolver function to update the resignation status, employee job info-inactive details, member status and asset details
module.exports.resignationEndpoint = async (event, context) => {
    let appManagerDbConnection;
    let organizationDbConnection;
    try {
        // require moment timezone
        let moment = require('moment-timezone');
        // get input data from response
        const response = JSON.parse(event.body).response;
        const resignation = JSON.parse(response.instance_data);
        const orgCode = event.headers.org_code ? event.headers.org_code : resignation.orgCode;
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            orgCode
        );
        console.log('Inside resignationEndpoint function: ',orgCode, response);
        // get resignationId
        let resignationId = resignation.resignationId;
        let workflowInstanceId = response.process_instance_id ? response.process_instance_id : "";
        // function to update resignation status,employee job info-inactive details, member status and asset details
        const updateResignationStatus = (resignationId, status,exitDate,employeeId,updatedByLogDetails) => {
            return new Promise(resolve => {
                let updateResignationDetails = {
                    Workflow_Status: Common.constant.statusCompleted,
                    Approval_Status: status,
                    Updated_By: updatedByLogDetails.updatedBy,
                    Updated_On: updatedByLogDetails.updatedOn
                };
                console.log('Input params for updating the resignation details.',updateResignationDetails)
                organizationDbConnection('emp_resignation')
                    .where('Resignation_Id', '=', resignationId)
                    .update(updateResignationDetails)
                    .then(async(result) => {
                        
                        let resignationData = {
                            resignationId: resignationId,
                            orgCode: orgCode,
                            updatedBy: updatedByLogDetails.updatedBy,
                            updatedOn : updatedByLogDetails.updatedOn,
                            approvedBy: updatedByLogDetails.updatedBy,
                            approvedOn : updatedByLogDetails.updatedOn,
                            approvalStatus: status
                        };

                        let instanceData = [];
                        instanceData.push({"instance_data" : JSON.stringify(resignation)});
                            
                        updateWorkflowResignationInstanceData(organizationDbConnection, workflowInstanceId, resignationData, "ta_process_instance_history", instanceData);
                        
                        // form system log params. Example system log: Update - Resignation - Approved status - 380
                        let systemLogParams = {
                            action: 'Update',
                            userIp: '',
                            employeeId: updatedByLogDetails.updatedBy,
                            formName: Common.constant.formResignation,
                            trackingColumn: status+' status',
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: resignationId
                        };

                        // Check status based on that update the details in emp_job and emp_user table
                        if (status === 'Approved'){
                            // get current date
                            let currentDate = moment().format('YYYY-MM-DD');
                            exitDate = moment(exitDate).format('YYYY-MM-DD');
                            
                            console.log('Default Reason Id:',Common.constant.defaultEsicReasonId,"currentDate",currentDate,"exitDate",exitDate);
                            // Check if exitDate is past date or not. if it is past date then InActive the employee and update default reason id
                            if (currentDate > exitDate) {
                                console.log('Current date is greater than the exit date.');

                                // form input params to update in emp_job table
                                let updateJson = {
                                    Emp_InActive_Date: exitDate,
                                    Emp_Status: 'InActive',
                                    Emp_Email: null,
                                    Reason_Id: Common.constant.defaultEsicReasonId
                                };
                                console.log('Input params for updating the job table',updateJson);
                                await organizationDbConnection('emp_job')
                                .update(updateJson)
                                .where('Employee_Id',employeeId)
                                .then(async(updateEmpStatus)=>{
                                    let updateParams={
                                        Member_Status : 'Inactive',
                                        Updated_On:updatedByLogDetails.updatedOn,
                                        Updated_By:updatedByLogDetails.updatedBy
                                    };
                                    console.log('Input params for updating the asset details.',updateParams);
                                    // update member status and asset mapping for the employee
                                    await updateStatusAndAssetMapping(organizationDbConnection,employeeId,updateParams,updatedByLogDetails.updatedOn);
                                    
                                    // get the current time based on mainbranch location
                                    let getOrgTimeZone = await commonLib.func.getMainBranchLocationTimeZone(organizationDbConnection);
                                    getOrgTimeZone ? getOrgTimeZone : 'Asia/Kolkata';
                                    let currentDate=moment().tz(getOrgTimeZone).format("YYYY-MM-DD[T]HH:mm:ss");
                                    await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.employeeInfoTimeStampLog,{Employee_Id:employeeId,Action:'InActive',Log_Timestamp:currentDate})
                                    // function to add system log activities
                                    await Common.createSystemLogActivities(systemLogParams);
                                })

                                appManagerDbConnection = await Helper.getAppManagerDbConnection(
                                    orgCode
                                );

                                /** Call function to revoke the access token for the inactive user */
                                await Common.revokeEmployeePotalAccess(organizationDbConnection, appManagerDbConnection, employeeId, orgCode);

                                /**Update resignation in camu if the camu integration is enabled for the instance */
                                await Common.callCamuResignationEndpoint(organizationDbConnection, appManagerDbConnection, employeeId, exitDate, orgCode);

                                const [airTicket, syntrumResignationActive] = await Promise.all([
                                    organizationDbConnection('emp_air_ticket_policy as EATP').where('Employee_Id', employeeId).where('Status', 'Active').first(),
                                    commonLib.func.integrationAPIIsEnabled(organizationDbConnection, {integrationType: 'Syntrum', direction: 'Push', action: 'Create', entityType: 'Resignation'})
                                ]);

                                if(airTicket){
                                    const ticketParams = { orgCode: orgCode, partnerId: '-', employeeIds: [employeeId], employeeIsInActiveAction: true};
                                    await commonLib.stepFunctions.triggerStepFunction(process.env.processAirTicketSummary, 'processAirTicketSummary', null, ticketParams);
                                }

                                if(syntrumResignationActive){
                                    const params = { orgCode: orgCode, partnerId: '-', employeeIds: [resignationId], entityType: 'resignation', action: 'create'};
                                    console.log('Syntrum resignation push to API request params => ', params);
                                    await commonLib.stepFunctions.triggerStepFunction(process.env.asyncSyntrumAPIStepFunction, 'asyncSyntrumAPIStepFunction', null, params);
                                }
                             
                                
                                // update Firebase_Uid as null in emp_user table
                                // So that employee cannot able to login
                                await organizationDbConnection('emp_user').update({ Firebase_Uid: null}).where('Employee_Id',employeeId);
                            }
                            resolve({ result: 'Success' });
                        }else{
                            console.log('Employee inactive details are not updated as resignation status is not approved.',status);
                            
                            // function to add system log activities
                            await Common.createSystemLogActivities(systemLogParams);
                            resolve({ result: 'Success' });
                        }
                    })
                    .catch(function(error) {
                        console.log('Error while updating the resignation status in resignationEndpoint()',error);
                        resolve({ error: error });
                    })
                    .finally(() => {
                        organizationDbConnection.destroy();
                        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                    })
            })
        }
        if (response.endTask) {
            const status = {
                1002: Common.constant.statusApproved,
                1007: Common.constant.statusRejected
            }[response.endTask.status_id]
            // Form updated on and updated by
            let updatedByLogDetails= {
                updatedBy: (response.endTask.completed_by)?response.endTask.completed_by:null,
                updatedOn: ''
            };
            //Get the login employee timezone. Example: Asia/Kolkata
            let timeZone = await Common.getEmployeeTimeZone(updatedByLogDetails.updatedBy,organizationDbConnection);
            // If the timezone is retrieved for the login employee
            if (timeZone && !timeZone.error) {
                updatedByLogDetails.updatedOn = Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss);
            }else{
                console.log('Error from the getEmployeeTimeZone() function.',timeZone);
                updatedByLogDetails.updatedOn = new Date();
            }
            // get resignation details
            let resignationDetails = await organizationDbConnection('emp_resignation').select('Employee_Id','Resignation_Date').where('Resignation_Id', resignationId);
            // check resignationDetails is not empty
            if(resignationDetails && resignationDetails.length > 0){
                // call function to update the status
                await updateResignationStatus(resignationId, status, resignationDetails[0].Resignation_Date, resignationDetails[0].Employee_Id,updatedByLogDetails);
            }else{
                console.log('Resignation status is not updated for the employee id as resignation does not exist.', resignationDetails);
            }
        }
    } catch (error) {
        console.log('Error in resignationEndpoint function in main catch block.', error);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
    }

    const response = {
        statusCode: 200,
        body: JSON.stringify({
            input: 'processed',
            errorCode: "",
            message: "Resignation status updated succesfully"
        })
    }
    return response;
}

module.exports.leaveEndpoint = async (event, context) => {
    console.log('Inside leaveEndPoint function', event);

    let organizationDbConnection;
    const FormData = require('form-data');
    try {
        const response = JSON.parse(event.body).response;
            
        const leaveData = JSON.parse(response.instance_data);
        
        if(leaveData && leaveData.leaveId && leaveData.employeeId &&
            leaveData.initiatorId && response.endTask.status_id && response.endTask.completed_by){
            
            const {leaveId, employeeId, initiatorId } = leaveData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code ? event.headers.org_code : leaveData.orgCode;

            let oldApprovalStatus = "";

            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );

            oldApprovalStatus = await getCurrentLeaveStatus(organizationDbConnection, leaveId);
            if(oldApprovalStatus){
                let isAction = oldApprovalStatus == "Applied" ? "Approve" : "CancelApprove";
                
                const workflowStatus = {
                    1002: oldApprovalStatus == "Applied" ? "Approved" : "Cancelled",
                    1007: oldApprovalStatus == "Applied" ? "Rejected" : "Approved"
                }[status_id]
                
                //Get the login employee timezone. Example: Asia/Kolkata
                let timeZone = await Common.getEmployeeTimeZone(completed_by,organizationDbConnection);
                
                let approvedOn = new Date();
                // If the timezone is retrieved for the login employee
                if (timeZone && !timeZone.error) {
                    approvedOn = Common.getFormattedDateString(timeZone,Common.constant.YYYYMMDDHHmmss);
                }
                let leaveStatusUpdate = '';
                //if(orgCode == 'hmcgroupuat' || orgCode == 'capricetest' || orgCode == 'fieldforce' || orgCode == 'advancepayroll'){
                    let leaveApprovalInputs = { 
                        leaveIds: [leaveId], //If multi approval is introduced then array of leave ids need to be send
                        leaveAction: status_id == 1002 ? 'approve' : 'reject',
                        orgCode,
                        currentDateTime: approvedOn,
                        completedBy: completed_by 
                    };
                    leaveStatusUpdate = await updateLeaveBalance(organizationDbConnection,leaveApprovalInputs); 
                    
                // }else{
                //     let bodyFormData = new FormData();
                //     bodyFormData.append('isAction', isAction);
                //     bodyFormData.append('Leave_Id', leaveId);
                //     bodyFormData.append('Approver_Id', completed_by);
                //     bodyFormData.append('Employee_Id', employeeId);
                //     bodyFormData.append('Added_By', initiatorId);
                //     bodyFormData.append('Status', workflowStatus); 
                //     bodyFormData.append('Comments', workflowStatus); 
                //     bodyFormData.append('Workflow_Status', Common.constant.statusCompleted);
                //     console.log(bodyFormData)

                //     let leaveStatusUpdateResult = await updateLeaveStatus(orgCode, bodyFormData, Common.constant.statusCompleted); 
                //     console.log("leaveStatusUpdateResult",leaveStatusUpdateResult)
                //     leaveStatusUpdate = {
                //         success: leaveStatusUpdateResult=='success' ? 1 : 0,
                //         message:leaveStatusUpdateResult
                //     };
                //     console.log("leaveStatusUpdate",leaveStatusUpdate)
                // }
            
                if(leaveStatusUpdate["success"]){
                    console.log("Insdie leaveStatusUpdate success block",leaveStatusUpdate)
                    let updateParams = {
                        Workflow_Status: Common.constant.statusCompleted,
                        Approved_On: approvedOn,
                        Approved_By: completed_by
                    };
                    let workflowStatusUpdate = await updateWorkflowStatus(organizationDbConnection, updateParams, leaveId);
                    updateParams.approvalStatus = workflowStatus;
                    
                    updateWorkflowLeaveInstanceData(organizationDbConnection, response.process_instance_id, updateParams, leaveData);
                    if(workflowStatusUpdate){
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        // return ({"errorCode":"", message: "Leave workflow status updated succesfully"});
                        const response = {
                            statusCode: 200,
                            body: JSON.stringify({
                                input : 'processed',
                                errorCode: "",
                                message: "Leave workflow status updated succesfully"
                            })
                        }
                        return response;
                    } else{ 
                        throw("EI00116");
                    }

                } else {
                    // rerurn the message we recieved from the php endpoint
                    const response = {
                        statusCode: 200,
                        body: JSON.stringify({
                            input : 'unprocessed',
                            errorCode: "EI00115",
                            message: leaveStatusUpdate.message
                        })
                    }
                    return response;
                }
            } else {
                console.log("Error while getting the current status of the leave record");
                throw("EI00114")
            }
            
        } else {
            console.log("Invalid input", leaveData);
            throw('IVE0000');
        }
            
        
    } catch (mainCatchError) {
        console.log('Error in workflowLeaveStatusUpdate function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'EI00117');
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;

    }
}

module.exports.preApprovalEndpoint = async (event, context) => {
    console.log('Inside preApprovalEndpoint function');

    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
        
        if(instanceData && instanceData.Pre_Approval_Id){

            const {Pre_Approval_Id, Status } = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code ? event.headers.org_code : instanceData.orgCode;

            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
        
            const workflowStatus = {
                1002: Status == "Applied" ? "Approved" : "Cancelled",
                1007: Status == "Applied" ? "Rejected" : "Approved"
            }[status_id]
            
            //Get the login employee timezone. Example: Asia/Kolkata
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approved_On": currentDateTime,
                "Approved_By": completed_by,
                "Status": workflowStatus
            } 
            
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'pre_approval_requests', updateParams, "Pre_Approval_Id", Pre_Approval_Id);
            instanceData.Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approved_By = completed_by;

            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData)
            
            if(updateResult){

                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "Leave workflow status updated succesfully"
                    })
                };
            } else{
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'unprocessed',
                        errorCode: errResult.code,
                        message: errResult.message
                    })
                }
            }
                
        } else {
            console.log("Invalid input", instanceData);
            throw('IVE0000');
        }   
            
    } catch (mainCatchError) {
        console.log('Error in workflowLeaveStatusUpdate function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'EI00117');
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;

    }
}
module.exports.timesheetStatusUpdateEndpoint = async (event, context) => {
    console.log('Inside timesheetStatusUpdateEndpoint function');
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
       
        if(instanceData && instanceData.Request_Id){
            const {Request_Id} = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code ? event.headers.org_code : instanceData.orgCode;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            const workflowStatus = {
                1002: "Approved",
                1007: "Rejected"
            }[status_id];
            //Get the login employee timezone. Example: Asia/Kolkata
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approved_On": currentDateTime,
                "Approver_Id": completed_by,
                "Approval_Status": workflowStatus
            };
            //Update status in the timesheet table
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'emp_timesheet', updateParams, "Request_Id", Request_Id);
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approver_Id = completed_by;
            //Update approval details in the instance data
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            if(updateResult){
                //Return response
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "timesheet workflow status updated successfully."
                    })
                };
            } else{
                console.log('timesheet status is not updated.',updateResult,updateParams);
                throw 'ELR0131';
            }   
        } else {
            console.log("Invalid input in the timesheetStatusUpdateEndpoint() function.");
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in timesheetStatusUpdateEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'ELR0015');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}

module.exports.recruitmentPosStatusUpdate = async (event, context) => {
    console.log('Inside recruitmentPosStatusUpdate function',event);
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
       
        if(instanceData && instanceData.recruitmentId){
            const {recruitmentId} = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            const workflowStatus = {
                1002: "Approved",
                1007: "Rejected"
            }[status_id];
            //Get the login employee timezone. Example: Asia/Kolkata
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approved_On": currentDateTime,
                "Approver_Id": completed_by,
                "Status": workflowStatus
            };
            //Update status in the timesheet table
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'mpp_recruitment_request', updateParams, "Recruitment_Id", recruitmentId);
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approver_Id = completed_by;
            //Update approval details in the instance data
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            try {
            if (workflowStatus === "Approved" && instanceData.customGroupId) {
                // Retrieve organization details
                const orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1);
                
                // Use instanceData.companyName if available; otherwise fallback to orgDetails.orgName
                const companyName = instanceData.companyName || orgDetails.orgName || '';
              
                // Build details string from department, division, and company names
                const details = [
                  instanceData.departmentName,
                  instanceData.divisionName,
                  companyName
                ].filter(Boolean).join(' / ') || 'N/A';
              
                // Construct the email subject with fallbacks
                const emailSubject = `Sourcing Request Raised – [${instanceData.positionTitle || 'N/A'}] | [${details}]`;
              
                // Get employee details and update instanceData accordingly
                const employeeDetails = await Common.getEmployeeEmail(instanceData.Added_By, organizationDbConnection);
                if (employeeDetails && employeeDetails.length > 0) {
                  const { Emp_Email, Added_By_Name } = employeeDetails[0];
                  instanceData.Added_By_Email = Emp_Email;
                  instanceData.Added_By_Name = Added_By_Name;
                }
              
                // Retrieve email body and replyTo details
                const { replyTo, finalEmailBody } = await Common.approvedAndForecastpositiondetails(
                  instanceData,
                  orgCode,
                  organizationDbConnection,
                  context,
                  orgDetails
                );
           //   console.log(replyTo,"replyTo",finalEmailBody,"finalEmailBody" ,emailSubject,"emailSubject");
                // Send the approved position email
                await Common.sendApprovedPositionEmail(
                  organizationDbConnection,
                  finalEmailBody,
                  emailSubject,
                  replyTo,
                  instanceData
                );
              }
            } catch (error) {
              console.error('Error sending approved position email:', error);
            }
              
            if(updateResult){
                let systemLogParams = {
                    action: 'Update',
                    userIp: '',
                    employeeId: completed_by,
                    formName: Common.constant.recruitmentRequest,
                    trackingColumn: workflowStatus+ 'status',
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: recruitmentId
                };
                await commonLib.func.createSystemLogActivities(systemLogParams);
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return response
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "Recruitment request status was updated successfully."
                    })
                };
            } else{
                console.log('recruitment request status is not updated.',updateResult,updateParams);
                throw 'ELR0132';
            }   
        } else {
            console.log("Invalid input in the recruitmentPosStatusUpdate() function.");
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in recruitmentPosStatusUpdate function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'EI00196');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}
module.exports.empTravelStatusUpdateEndpoint = async (event, context) => {
    console.log('Inside empTravelStatusUpdateEndpoint function',event.body);
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
       
        if(instanceData && instanceData.Request_Id){
            const {Request_Id,Status} = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            previousStatus = Status.toLowerCase();
            const workflowStatus = {
                1002: previousStatus == "applied" ? "Approved" : "Cancelled",
                1007: previousStatus == "applied" ? "Rejected" : "Approved"
            }[status_id]
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Status": workflowStatus
            };
            //Update status in the timesheet table
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'emp_travel_request', updateParams, "Request_Id", Request_Id);
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approver_Id = completed_by;
            //Update approval details in the instance data
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            const emailData = await organizationDbConnection('employee_travel_setting')
            .select('Email_Recipients','Additional_Recipients','Employee_Travel_Setting_Id')
            .orderBy('Employee_Travel_Setting_Id', 'desc') // Fetch the latest email settings
            .first();
           
            if((workflowStatus == "Approved" || workflowStatus == "Cancelled") && emailData && emailData.Email_Recipients){
               let result =await Common.retrieveEmployeeTravel(organizationDbConnection, Request_Id);
               let emailSubject = workflowStatus === "Approved" ? "Travel Request Approved" : "Travel Request Cancelled";
               emailSubject += ` regarding ${result.employeeName} (Employee ID: ${instanceData.Employee_Id}) for the travel period from ${instanceData.Travel_Start_Date} to ${instanceData.Travel_End_Date}`;
               let { replyTo,
                finalEmailBody} = await Common.travelDetailsTemplate(result,orgCode, organizationDbConnection, context,instanceData.Employee_Id);
               await Common.sendCustomEmail(organizationDbConnection,finalEmailBody,emailSubject,replyTo)
            }
          
            if(updateResult){
                let systemLogParams = {
                    action: 'Update',
                    userIp: '',
                    employeeId: completed_by,
                    formName: Common.constant.travelRequest,
                    trackingColumn: workflowStatus+ 'status',
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: Request_Id
                };
                //Return response
                await commonLib.func.createSystemLogActivities(systemLogParams);
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "Employee Travel request status was updated successfully."
                    })
                };
            } else{
                console.log('Employee Travel request status is not updated.',updateResult,updateParams);
                throw 'EI00197';
            }   
        } else {
            console.log("Invalid input in the empTravelStatusUpdateEndpoint() function.");
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in empTravelStatusUpdateEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'ELR0136');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}
module.exports.compOffEndpoint = async (event, context) => {
    console.log('Inside compOffEndpoint function',event.body);
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
       
        if(instanceData && response.process_instance_id){
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            let previousStatus = instanceData.Approval_Status.toLowerCase();
            const workflowStatus = {
                1002: previousStatus == "applied" ? "Approved" : "Cancelled",
                1007: previousStatus == "applied" ? "Rejected" : "Approved"
            }[status_id]
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approval_Status": workflowStatus
            };
            let compOffBeforeUpdate = await
            organizationDbConnection('compensatory_off')
            .select('*')
            .where('Employee_Id',instanceData.employeeId)
            .where('Compensatory_Date',instanceData.Compensatory_Date)
            .then()
            console.log(compOffBeforeUpdate,orgCode)

            let summaryDetails = [];
            summaryDetails.push({
                Employee_Id: instanceData.employeeId,
                Summary_Date: instanceData.Compensatory_Date
            });
            //Update status in the timesheet table
            let updateResult = await updateTableBasedOnConditionSync(organizationDbConnection, 'compensatory_off', updateParams, "Process_Instance_Id", response.process_instance_id, { summaryDetails, orgCode, currentDateTime });
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approver_Id = completed_by;

            //Update approval details in the instance data
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            if(updateResult){
                //update comp-off balance
                const getCompBalance = await organizationDbConnection('compensatory_off AS C')
                                        .select('C.Duration', 'CB.Comp_Off_Balance_Id', 'CB.Total_Days', 'CB.Remaining_Days')
                                        .join('compensatory_off_balance AS CB', 'C.Comp_Off_Balance_Id', 'CB.Comp_Off_Balance_Id')
                                        .where('C.Process_Instance_Id', response.process_instance_id)
                                        .first();
                
                if(getCompBalance){
                    let remainingDays = getCompBalance.Remaining_Days; // Initialize with the existing value

                    if (workflowStatus === 'Approved') {
                        remainingDays -= getCompBalance.Duration;
                    } else if (workflowStatus === 'Cancelled') {
                        remainingDays += getCompBalance.Duration;
                    }

                    // Update the table only if remainingDays has changed
                    if (remainingDays !== getCompBalance.Remaining_Days) {
                        let updateBalance = await commonLib.func.updateTableBasedOnCondition(
                            organizationDbConnection, 
                            'compensatory_off_balance', 
                            { "Remaining_Days": remainingDays }, 
                            "Comp_Off_Balance_Id", 
                            getCompBalance.Comp_Off_Balance_Id
                        );

                        if (!updateBalance) {
                            throw 'WFT0103'; // Consider using an Error object instead of a string
                        }
                    }
                } 
                                        
                // let systemLogParams = {
                //     action: 'Update',
                //     userIp: '',
                //     employeeId: completed_by,
                //     formName: Common.constant.compOff,
                //     trackingColumn: workflowStatus+ 'status',
                //     organizationDbConnection: organizationDbConnection,
                //     uniqueId: Process_Instance_Id // need to change
                // };
                // //Return response
                // await commonLib.func.createSystemLogActivities(systemLogParams);
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "Comp off request status was updated successfully."
                    })
                };
            } else{
                console.log('Comp off request status is not updated.',updateResult,updateParams);
                throw 'WFT0103';
            }   
        } else {
            console.log("Invalid input in the compOffEndpoint() function.");
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in compOffEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'WFT0103');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}

module.exports.newPositionStatusUpdateEndpoint = async (event, context) => {
    console.log('Inside newPositionStatusUpdateEndpoint function',event.body);
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
       
        if(instanceData && instanceData.positionRequestId){
            const {positionRequestId} = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            const workflowStatus = {
                1002: "TO In Review",
                1007: "Rejected"
            }[status_id];
            //Get the login employee timezone. Example: Asia/Kolkata
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approved_On": currentDateTime,
                "Approver_Id": completed_by,
                "Status": workflowStatus
            };
            //Update status in the timesheet table
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'mpp_position_request', updateParams, "Position_Request_Id", positionRequestId);
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approver_Id = completed_by;
            //Update approval details in the instance data
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            try {
                if (workflowStatus.toLowerCase() === "to in review" && instanceData.customGroupId) {
                  // Retrieve organization details
                  const orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1);
                  
                  // Use instanceData.companyName if available; otherwise fallback to orgDetails.orgName
                  const companyName = instanceData.companyName || orgDetails.orgName || '';
                
                  // Build details string from department, division, and company names
                  const details = [
                    instanceData.departmentName,
                    instanceData.divisionName,
                    companyName
                  ].filter(Boolean).join(' / ') || 'N/A';
                
                  // Construct the email subject with fallbacks
                  const emailSubject = (!instanceData?.positionCode || instanceData.positionCode.length <= 0)
                  ? `Sourcing Request Raised – [${instanceData?.positionTitle || 'N/A'}] | [${details || 'N/A'}]`
                  : `Additional Manning Approved – Initiate Sourcing Process | [${instanceData?.positionTitle || 'N/A'}-${instanceData?.positionCode || 'N/A'}] | [${details || 'N/A'}]`;
                
                  // Get employee details and update instanceData accordingly
                  const employeeDetails = await Common.getEmployeeEmail(instanceData.Added_By, organizationDbConnection);
                  if (employeeDetails && employeeDetails.length > 0) {
                    const { Emp_Email, Added_By_Name } = employeeDetails[0];
                    instanceData.Added_By_Email = Emp_Email;
                    instanceData.Added_By_Name = Added_By_Name;
                  }
                
                  // Retrieve email body and replyTo details
                  const { replyTo, finalEmailBody } = await Common.newpositiondetails(
                    instanceData,
                    orgCode,
                    organizationDbConnection,
                    context,
                    orgDetails
                  );
                
                  // Send the approved position email
                  await Common.sendApprovedPositionEmail(
                    organizationDbConnection,
                    finalEmailBody,
                    emailSubject,
                    replyTo,
                    instanceData
                  );
                }
              } catch (error) {
                console.error("Error processing approved position email:", error);
              }
              
            if(updateResult){
                let systemLogParams = {
                    action: 'Update',
                    userIp: '',
                    employeeId: completed_by,
                    formName: Common.constant.newPosition,
                    trackingColumn: workflowStatus+ 'status',
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: positionRequestId
                };
                //Return response
                await commonLib.func.createSystemLogActivities(systemLogParams);
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "New Position request status was updated successfully."
                    })
                };
            } else{
                console.log('New Position request status is not updated.',updateResult,updateParams);
                throw 'EI00197';
            }   
        } else {
            console.log("Invalid input in the newPositionStatusUpdateEndpoint() function.");
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in newPositionStatusUpdateEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'ELR0135');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}

module.exports.lopRecoveryEndpoint = async (event, context) => {
    console.log('Inside lopRecoveryEndpoint function');
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
       
        if(instanceData && instanceData.Lop_Recovery_Id){
            const {Lop_Recovery_Id} = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code ? event.headers.org_code : instanceData.orgCode;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            const workflowStatus = {
                1002: "Approved",
                1007: "Rejected"
            }[status_id];
            //Get the login employee timezone. Example: Asia/Kolkata
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approved_On": currentDateTime,
                "Approved_By": completed_by,
                "Approval_Status": workflowStatus
            };
            //Update status in the lop recovery table
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'employee_lop_recovery', updateParams, "Lop_Recovery_Id", Lop_Recovery_Id);
            instanceData.Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approved_By = completed_by;
            //Update approval details in the instance data
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            if(updateResult){
                //Return response
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "Lop Recovery workflow status updated successfully."
                    })
                };
            } else{
                console.log('LOP recovery status is not updated.',updateResult,updateParams);
                throw 'ELR0122';
            }   
        } else {
            console.log("Invalid input in the lopRecoveryEndpoint() function.");
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in lopRecoveryEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'ELR0014');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}

//Function to update the reimbursement request - approval status when the reimbursment status is updated thru workflow
module.exports.reimbursementEndpoint = async (event, context) => {
    console.log('Inside reimbursementEndpoint function');
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
        console.log('Inside reimbursementEndpoint function','response',response,instanceData);
        if(instanceData && instanceData.requestId){
            const {requestId} = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code ? event.headers.org_code : instanceData.orgCode;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            const workflowStatus = {
                1002: "Complete",
                1007: "Rejected"
            }[status_id];
            //Get the current date time in utc
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approved_On": currentDateTime,
                "Approved_By": completed_by,
                "Approval_Status": workflowStatus
            };
            //Update status in the reimbursement table
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'reimbursement', updateParams, "Request_Id", requestId);
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approved_By = completed_by;
            //Update instance data in ta_process_instance_history table
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            if(updateResult){
                //Return response
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "Reimbursement workflow status updated successfully."
                    })
                };
            } else{
                console.log('Reimbursement status is not updated.',updateResult,updateParams,instanceData);
                throw 'PRE0101';
            }   
        } else {
            console.log("Invalid input in the reimbursementEndpoint() function.",instanceData);
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in reimbursementEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'PRE0001');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}
module.exports.salaryRevisionStatusUpdateEndpoint = async (event, context) => {
    console.log('Inside salaryRevisionStatusUpdateEndpoint function',event.body);
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);
        console.log('Inside salaryRevisionStatusUpdateEndpoint function','response',response,instanceData);
        if(instanceData && instanceData.id){
            const {id} = instanceData;
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code ? event.headers.org_code : instanceData.orgCode;
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            const workflowStatus = {
                1002: "Approved",
                1007: "Rejected"
            }[status_id];
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Revision_Status": workflowStatus
            };
            //Update status in the salary_revision_details table
            let updateResult = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, 'salary_revision_details', updateParams, "Revision_Id", id);
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approved_By = completed_by;
            if(workflowStatus?.toLowerCase() == "approved"){
            await addSalaryHistoryRecords(organizationDbConnection, instanceData, completed_by, currentDateTime,orgCode);
            }
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            if(updateResult){
                //Return response
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "salary revision workflow status updated successfully."
                    })
                };
            } else{
                console.log('salary revision status is not updated.',updateResult,updateParams,instanceData);
                throw 'PRE0101';
            }   
        } else {
            console.log("Invalid input in the salaryRevisionStatusUpdateEndpoint() function.",instanceData);
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in salaryRevisionStatusUpdateEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'SLR0001');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}


//Function to get the old leave status.
async function getCurrentLeaveStatus(organizationDbConnection, leaveId){
    try {
        console.log("Inside getCurrentLeaveStatus function");
        return(
            organizationDbConnection('emp_leaves')
            .select('Approval_Status')
            .where('Leave_Id',leaveId)
            .then(approvalStatusData=>{
                if(approvalStatusData.length > 0) {
                    oldApprovalStatus = approvalStatusData[0].Approval_Status ;
                    return oldApprovalStatus;
                } else{
                    console.log("Leave record does not exists: ", leaveId);
                    return false;
                } 
            })
            .catch(error => {
                console.log('Error while getting the getCurrentLeaveStatus .catch block.',error);
                return false;
            })
        )
    } catch(getCurrentLeaveStatusError) {
        console.log('Error in getCurrentLeaveStatus function', getCurrentLeaveStatusError);
        return false;
    }
}

//Function to update the leave status by calling the leave-status action in the leaves.
async function updateLeaveStatus(orgCode, bodyFormData, workflowStatus){
    
    return new Promise(function(resolve, reject) {
        try {
            const axios = require('axios');
            const config={
                method: 'post',
                maxBodyLength: Infinity,
                url: "https://"+orgCode+"."+process.env.leaveStatusDomainName+"/employees/leaves/leave-status",
                data: bodyFormData,
                headers: bodyFormData.getHeaders()
            }
            axios.request(config)
            .then(function(response) {
                if (response.status == 200 && response.data && response.data.success) {
                    console.log("Leave status update endpoint integaration response,", response.data);
                    resolve("success");
                }
                else{
                    console.log("Error in the leave status update endpoint.", response);
                    resolve((response.data && response.data.msg) ? response.data.msg : "Error while processing the request to update the leave status. Please contact the system admin.");
                }
            })
            .catch(function(error) {
                console.log("Error while calling the leave status update endpoint.", error);
                resolve("Error while processing the request to update the leave status. Please contact the system admin.");
            })
        } catch(updateLeaveStatusError) {
            console.log('Error in updateLeaveStatus function', updateLeaveStatusError);
            resolve("Error while processing the request to update the leave status. Please contact the system admin.");
        }
    })
}

async function updateWorkflowStatus(organizationDbConnection, updateParams, leaveId){
    return new Promise(function(resolve, reject) {
        try{
            console.log("Inside updateWorkflowStatus function");
            //update workflow status
            organizationDbConnection('emp_leaves')
                .where('Leave_Id', '=', leaveId)
                .update(updateParams)
                .then(async(result) => {
                    resolve(true);
                }).catch(function(error) {
                    console.log('Error while updating the leave workflow status',error);
                    resolve(false);
                }) 
        } catch(updateWorkflowStatusError){
            console.log('Error in updateWorkflowStatus function', updateWorkflowStatusError);
            resolve(false);
        }
    });
}

module.exports.getEmployeeManagerId =  async (event, context) => {
    const response = JSON.parse(event.body).response;
    try {
        // get orgCode from envelope
        const organizationDbConnection = await Helper.getOrganizationDbConnection(
            event.headers.org_code
        );
        
        let employeeId = 0;
        if (response.employeeId || response.Employee_Id) {
            // for resignation workflow schema
            employeeId = response.employeeId ? response.employeeId : response.Employee_Id;
        } else if (response.addedBy) {
            // for job post workflow schema
            employeeId = response.addedBy;
        }

        let empManagerId = await organizationDbConnection
            .select(
                'A.Manager_Id'
            )
            .from('emp_job as A')
            .innerJoin('emp_job as B', 'A.Manager_Id','B.Employee_Id')
            .where('A.Employee_Id', employeeId)
            .where('B.Emp_Status', 'Active')
            .then(result => {
                return result;
            })
            .catch(function(error) {
                console.log('Error while call the `getEmployeeManagerId()`', error)
                return []
            });
            
            //If the manager is not associated with the employee, then get the super admin employee id
            if (empManagerId.length > 0 && empManagerId[0].Manager_Id) {
                return { statusCode:200, body: empManagerId[0].Manager_Id.toString() };
            } else{
                    let superAdminEmployeeId = await commonLib.func.getSuperAdminEmployeeId(organizationDbConnection);
                    if(superAdminEmployeeId)
                        return {statusCode:200, body: superAdminEmployeeId.Employee_Id.toString()}
                    else
                        return { statusCode:500, body: "0" };
            }

    } catch (error) {
        console.log(`Error while call the 'getEmployeeManagerId()' api`, error)
        return { statusCode:500, body: "0" };
    }
}

module.exports.shortTimeOffEndpoint = async (event, context) => {
    console.log('Inside shortTimeOffEndpoint function',event.body);
    let organizationDbConnection;
    let moment = require('moment-timezone');
    try {
        const response = JSON.parse(event.body).response;
        const instanceData = JSON.parse(response.instance_data);

        if(instanceData && response.process_instance_id){
            const {status_id, completed_by} = response.endTask;
            const orgCode = event.headers.org_code;
            //Get Database Connection
            organizationDbConnection = await Helper.getOrganizationDbConnection(
                orgCode
            );
            let previousStatus = instanceData.Approval_Status.toLowerCase();
            const workflowStatus = {
                1002: previousStatus == "applied" ? "Approved" : "Cancelled",
                1007: previousStatus == "applied" ? "Rejected" : "Approved"
            }[status_id]
            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let updateParams = {
                "Approval_Status": workflowStatus
            };
           
            let summaryDetails = [];
            summaryDetails.push({
                Employee_Id: instanceData.employeeId,
                Summary_Date: instanceData.Short_Time_Off_Date
            });
            //Update status in the short time off table
            let updateResult = await updateTableBasedOnConditionSync(organizationDbConnection, 'short_time_off', updateParams, "Process_Instance_Id", response.process_instance_id, { summaryDetails, orgCode, currentDateTime });
            instanceData.Approval_Status = workflowStatus;
            instanceData.Approved_On = currentDateTime;
            instanceData.Approver_Id = completed_by;
            
            //Update approval details in the instance data
            updatePreApprovalRequestInstanceData(organizationDbConnection, response.process_instance_id, instanceData);
            if(updateResult){
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        input : 'processed',
                        errorCode: "",
                        message: "Short time off request status was updated successfully."
                    })
                };
            } else{
                console.log('Short time off request status is not updated.',updateResult,updateParams);
                throw 'WFT0105';
            }
        } else {
            console.log("Invalid input in the shortTimeOffEndpoint() function.");
            throw('IVE0000');
        }
    } catch (mainCatchError) {
        console.log('Error in shortTimeOffEndpoint function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'WFT0105');
        //Return response
        const response = {
            statusCode: 200,
            body: JSON.stringify({
                input : 'unprocessed',
                errorCode: errResult.code,
                message: errResult.message
            })
        }
        return response;
    }
}

module.exports.getSecondLineManager =  async (event, context) => {
    const response = JSON.parse(event.body).response;
    let organizationDbConnection;
    try {
        console.log("Inside getSecondLineManager function");
        // get orgCode from envelope
        organizationDbConnection = await Helper.getOrganizationDbConnection(
            event.headers.org_code
        );
        
        let employeeId = 0;
        if (response.employeeId) {
            // for resignation workflow schema
            employeeId = response.employeeId;
        } else if (response.addedBy) {
            // for job post workflow schema
            employeeId = response.addedBy;
        } else {
            return { statusCode:500, body: "0" };
        }

        let managerId = await commonLib.func.getSecondLineManager(organizationDbConnection, employeeId);
        if(managerId ){
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {statusCode:200, body: managerId.toString()}
        } else {
            let superAdminEmployeeId = await commonLib.func.getSuperAdminEmployeeId(organizationDbConnection);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            if(superAdminEmployeeId)
                return {statusCode:200, body: superAdminEmployeeId.Employee_Id.toString()}
            else
                return { statusCode:500, body: "0" };
        }

    } catch (error) {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error while call the getSecondLineManager function', error)
        return { statusCode:500, body: "0" };
    }
}

/**
 * Checks whether the given status is one of the valid trigger statuses 
 * for skipping or bypassing the attendance summary process.
 *
 * @param {string} status - The workflow status to validate.
 * @returns {boolean} - Returns true if the status is 'Rejected' or 'Cancelled', false otherwise.
 */
function validateAttendanceSummaryStatus(status) {
    const triggerAttendanceSummaryStatus = ['Rejected', 'Cancelled'];
    return triggerAttendanceSummaryStatus.includes(status);
}

// function to update table based on condition
async function updateTableBasedOnConditionSync(anyDbConnection, tableName, inputParams, columnName, conditionData, otherDetails) {
  try {
    let { summaryDetails } = otherDetails;
    const result = await anyDbConnection(tableName)
    .update(inputParams)
    .where(columnName, conditionData)
    .then(async(result) => {
        console.log("updateTableBasedOnConditionSync result",result,inputParams,conditionData,summaryDetails)

        if(result > 0){
            let isValidAttendanceSummaryStatus = validateAttendanceSummaryStatus(inputParams.Approval_Status);
            //If the approval status is rejected or cancelled created for early checkout then ignore the record or trigger attendance summary
            if(isValidAttendanceSummaryStatus){
                otherDetails.inputParams = inputParams;
                otherDetails.tableName = tableName;
                let attendanceSummaryResult  = await validateAndTriggerAttendanceSummary(anyDbConnection,otherDetails);
                return attendanceSummaryResult;
            }
            return true;
        }else{
            return false;
        }
    });
    return result;
  }
  catch (error) {
    console.log('Error in updateTableBasedOnConditionSync function main catch block', error);
    return false;
  }
}

async function addSalaryHistoryRecords(organizationDbConnection, instanceData, completed_by, currentDateTime, orgCode) {
    let moment = require('moment-timezone');
    try {
        if(!instanceData || !instanceData.employeeId || !instanceData.id){
            console.log('Invalid instance data in the addSalaryHistoryRecords function',instanceData);
            return;
        }
        return (await organizationDbConnection.transaction(async (trx) => {
            const employeeId = instanceData.employeeId;
            const revisionId = instanceData.id;
            
            // Calculate Effective_To date (effectiveFrom - 1 day)
            const revisionDetails = await organizationDbConnection('salary_revision_details')
            .select('*')
            .where('Revision_Id', revisionId)
            .first()
            .transacting(trx);
            const [month, year] = revisionDetails.Salary_Effective_Month?.split(',')
            let { Salary_Date } = await commonLib.func.getSalaryDay(
                    orgCode,
                    organizationDbConnection,
                    month,
                    year
                );
            let effectiveToDate = '';
            if(Salary_Date){
                effectiveToDate = moment(Salary_Date).subtract(1, 'day').format('YYYY-MM-DD');
            }
            // Step 1: Get current employee salary details
            const currentSalaryDetails = await organizationDbConnection('employee_salary_details')
                .select('*')
                .where('Employee_Id', employeeId)
                .first()
                .transacting(trx);
            if (currentSalaryDetails) {
                // Insert into employee_salary_history
                const salaryHistoryData = {
                    Employee_Id: currentSalaryDetails.Employee_Id,
                    Template_Id: currentSalaryDetails.Template_Id,
                    Effective_From: currentSalaryDetails.Effective_From,
                    Effective_To: effectiveToDate,
                    Annual_Ctc: currentSalaryDetails.Annual_Ctc,
                    Annual_Gross_Salary: currentSalaryDetails.Annual_Gross_Salary,
                    Monthly_Gross_Salary: currentSalaryDetails.Monthly_Gross_Salary,
                    Added_On: currentDateTime,
                    Added_By: completed_by,
                    Salary_Effective_Month: currentSalaryDetails.Salary_Effective_Month
                };
                const [salaryHistoryId] = await organizationDbConnection('employee_salary_history')
                    .insert(salaryHistoryData)
                    .transacting(trx);
                // Step 2: Get current employee allowances
                const currentAllowances = await organizationDbConnection('employee_salary_allowance')
                    .select('*')
                    .where('Employee_Id', employeeId)
                    .transacting(trx);
                if (currentAllowances.length > 0) {
                    const allowanceHistoryData = currentAllowances.map(allowance => ({
                        Salary_History_Id: salaryHistoryId,
                        Allowance_Id: allowance.Allowance_Id,
                        Allowance_Type: allowance.Allowance_Type,
                        Percentage: allowance.Percentage,
                        Amount: allowance.Amount,
                        Allowance_Wages: allowance.Allowance_Wages
                    }));
                    await organizationDbConnection('employee_allowance_history')
                        .insert(allowanceHistoryData)
                        .transacting(trx);
                }
                // Step 3: Get current employee retirals
                const currentRetirals = await organizationDbConnection('employee_salary_retirals')
                    .select('*')
                    .where('Employee_Id', employeeId)
                    .transacting(trx);
                if (currentRetirals.length > 0) {
                    const retiralsHistoryData = currentRetirals.map(retiral => ({
                        Salary_History_Id: salaryHistoryId,
                        Form_Id: retiral.Form_Id,
                        Retirals_Id: retiral.Retirals_Id,
                        Retirals_Type: retiral.Retirals_Type,
                        Employer_Retiral_Wages: retiral.Employer_Retiral_Wages,
                        Employee_Retiral_Wages: retiral.Employee_Retiral_Wages,
                        Employee_Share_Percentage: retiral.Employee_Share_Percentage,
                        Employer_Share_Percentage: retiral.Employer_Share_Percentage,
                        Employee_Share_Amount: retiral.Employee_Share_Amount,
                        Employer_Share_Amount: retiral.Employer_Share_Amount,
                        PF_Employee_Contribution: retiral.PF_Employee_Contribution,
                        PF_Employer_Contribution: retiral.PF_Employer_Contribution,
                        Employee_Statutory_Limit: retiral.Employee_Statutory_Limit,
                        Employer_Statutory_Limit: retiral.Employer_Statutory_Limit,
                        Eligible_For_EPS: retiral.Eligible_For_EPS,
                        Admin_Charge: retiral.Admin_Charge,
                        EDLI_Charge: retiral.EDLI_Charge
                    }));
                    await organizationDbConnection('employee_retirals_history')
                        .insert(retiralsHistoryData)
                        .transacting(trx);
                }
            }
            //delete employee salary records
            await organizationDbConnection('employee_salary_details')
                .where('Employee_Id', employeeId)
                .del()
                .transacting(trx);
            //delete employee allowance records
            await organizationDbConnection('employee_salary_allowance')
                .where('Employee_Id', employeeId)
                .del()
                .transacting(trx);
            //delete employee retirals records
            await organizationDbConnection('employee_salary_retirals')
                .where('Employee_Id', employeeId)
                .del()
                .transacting(trx);
            // Step 4: Update main employee salary tables with revision dat
            if (revisionDetails) {
                // Update employee_salary_details with revision data
                const updatedSalaryData = {
                    Template_Id: revisionDetails.Template_Id,
                    Employee_Id: revisionDetails.Employee_Id,
                    Effective_From: Salary_Date,
                    Annual_Ctc: revisionDetails.Annual_Ctc,
                    Annual_Gross_Salary: revisionDetails.Annual_Gross_Salary,
                    Monthly_Gross_Salary: revisionDetails.Monthly_Gross_Salary,
                    Updated_On: currentDateTime,
                    Updated_By: completed_by,
                    Salary_Effective_Month: revisionDetails.Salary_Effective_Month,
                    Added_On: currentDateTime,
                    Added_By: completed_by
                };
                await organizationDbConnection('employee_salary_details')
                    .insert(updatedSalaryData)
                    .transacting(trx);
                const revisionAllowances = await organizationDbConnection('salary_revision_allowance')
                    .select('*')
                    .where('Revision_Id', revisionId)
                    .transacting(trx);
                if (revisionAllowances.length > 0) {
                    const newAllowanceData = revisionAllowances.map(allowance => ({
                        Employee_Id: employeeId,
                        Allowance_Id: allowance.Allowance_Id,
                        Allowance_Type: allowance.Allowance_Type,
                        Percentage: allowance.Percentage,
                        Amount: allowance.Amount,
                        Allowance_Wages: allowance.Allowance_Wages
                    }));
                    await organizationDbConnection('employee_salary_allowance')
                        .insert(newAllowanceData)
                        .transacting(trx);
                }
                const revisionRetirals = await organizationDbConnection('salary_revision_retirals')
                    .select('*')
                    .where('Revision_Id', revisionId)
                    .transacting(trx);
                if (revisionRetirals.length > 0) {
                    const newRetiralsData = revisionRetirals.map(retiral => ({
                        Employee_Id: employeeId,
                        Form_Id: retiral.Form_Id,
                        Retirals_Id: retiral.Retirals_Id,
                        Retirals_Type: retiral.Retirals_Type,
                        Employer_Retiral_Wages: retiral.Employer_Retiral_Wages,
                        Employee_Retiral_Wages: retiral.Employee_Retiral_Wages,
                        Employee_Share_Percentage: retiral.Employee_Share_Percentage,
                        Employer_Share_Percentage: retiral.Employer_Share_Percentage,
                        Employee_Share_Amount: retiral.Employee_Share_Amount,
                        Employer_Share_Amount: retiral.Employer_Share_Amount,
                        PF_Employee_Contribution: retiral.PF_Employee_Contribution,
                        PF_Employer_Contribution: retiral.PF_Employer_Contribution,
                        Employee_Statutory_Limit: retiral.Employee_Statutory_Limit,
                        Employer_Statutory_Limit: retiral.Employer_Statutory_Limit,
                        Eligible_For_EPS: retiral.Eligible_For_EPS,
                        Admin_Charge: retiral.Admin_Charge,
                        EDLI_Charge: retiral.EDLI_Charge
                    }));
                    await organizationDbConnection('employee_salary_retirals')
                        .insert(newRetiralsData)
                        .transacting(trx);
                }

                // Handle old salary table operations after inserting new salary records
                const oldTableArgs = {
                    employeeId: employeeId,
                    effectiveFrom: Salary_Date,
                    annualCTC: revisionDetails.Annual_Ctc,
                    annualGrossSalary: revisionDetails.Annual_Gross_Salary,
                    monthlyGrossSalary: revisionDetails.Monthly_Gross_Salary,
                    allowance: revisionAllowances.map(allowance => ({
                        allowanceId: allowance.Allowance_Id,
                        allowanceType: allowance.Allowance_Type,
                        percentage: allowance.Percentage,
                        amount: allowance.Amount
                    })),
                    retirals: revisionRetirals.map(retiral => ({
                        formId: retiral.Form_Id,
                        retiralsId: retiral.Retirals_Id,
                        retiralsType: retiral.Retirals_Type,
                        employeeSharePercentage: retiral.Employee_Share_Percentage,
                        employerSharePercentage: retiral.Employer_Share_Percentage,
                        employeeShareAmount: retiral.Employee_Share_Amount,
                        employerShareAmount: retiral.Employer_Share_Amount,
                        pfEmployeeContribution: retiral.PF_Employee_Contribution,
                        pfEmployerContribution: retiral.PF_Employer_Contribution,
                        employeeStatutoryLimit: retiral.Employee_Statutory_Limit,
                        employerStatutoryLimit: retiral.Employer_Statutory_Limit,
                        eligibleForEPS: retiral.Eligible_For_EPS,
                        adminCharge: retiral.Admin_Charge,
                        edliCharge: retiral.EDLI_Charge
                    })),
                    formId: 207 // Salary form
                };

                await handleOldSalaryTableOperationsForRevision(
                    organizationDbConnection,
                    oldTableArgs,
                    completed_by,
                    currentDateTime,
                    trx
                );
            }

            console.log('Salary history records (current + revision) added successfully for employee:', employeeId);
            return 'success';
        }));
    } catch (error) {
        console.log('Error in addSalaryHistoryRecords function:', error);
        throw error;
    }
}

async function handleOldSalaryTableOperationsForRevision(organizationDbConnection, args, loginEmployeeId, currentTimestamp, trx) {
    try {
      console.log('Inside handleOldSalaryTableOperationsForRevision function',args);
  
      // Get employee job details for Location_Id and Grade_Id
      const empJobDetails = await organizationDbConnection(ehrTables.empJob)
        .select('Location_Id', 'Salary_Grade_Id as Grade_Id')
        .where('Employee_Id', args.employeeId)
        .first()
        .transacting(trx);
  
      // Get basic pay from employee_salary_allowance where Is_Basic_Pay = 'Yes'
      const basicPayRecord = await organizationDbConnection(ehrTables.employeeSalaryAllowance + ' as ESA')
        .select('ESA.Amount')
        .innerJoin(ehrTables.allowances + ' as A', 'A.Allowance_Id', 'ESA.Allowance_Id')
        .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
        .where('ESA.Employee_Id', args.employeeId)
        .where('AT.Is_Basic_Pay', 'Yes')
        .first()
        .transacting(trx);
  
      // Get salary configuration details
      const salaryConfig = await organizationDbConnection(ehrTables.employeeSalaryConfiguration)
        .select('*')
        .where('Employee_Id', args.employeeId)
        .first()
        .transacting(trx);
  
      // Use revision retirals data instead of employee retirals
      const retiralsDetails = args.retirals || [];
  
      // Prepare contribution rates based on revision retirals
      let employeeContributionRate = null;
      let employerContributionRate = null;
      let gratuityAmount = null;
  
      if (retiralsDetails && retiralsDetails.length > 0) {
        const pfRetiral = retiralsDetails.find(r => r.formId == 52); // Form_Id 52 is for PF
        const gratuityRetiral = retiralsDetails.find(r => r.formId == 110); // Form_Id 110 is for Gratuity
  
        if (pfRetiral) {
          // Set contribution rates based on PF_Employee_Contribution and PF_Employer_Contribution
          if (pfRetiral.pfEmployeeContribution === 'Restrict_Pf_Wages') {
            employeeContributionRate = 'Restrict Contribution to ₹15,000 of PF Wage';
          } else if (pfRetiral.pfEmployeeContribution === 'Actual') {
            employeeContributionRate = '12% of Actual PF Wage';
          }
  
          if (pfRetiral.pfEmployerContribution === 'Restrict_Pf_Wages') {
            employerContributionRate = 'Restrict Contribution to ₹15,000 of PF Wage';
          } else if (pfRetiral.pfEmployerContribution === 'Actual') {
            employerContributionRate = '12% of Actual PF Wage';
          }
        }
  
        if (gratuityRetiral) {
          gratuityAmount = gratuityRetiral.employerShareAmount;
        }
      }
  
      // Check if record already exists in salary_details table
      const existingSalaryRecord = await organizationDbConnection('salary_details')
        .select('*')
        .where('Employee_Id', args.employeeId)
        .first()
        .transacting(trx);
  
      if (existingSalaryRecord) {
        // Handle case where record exists in old salary table - move existing record to history if there are changes
        await handleSalaryHistoryAndUpdate(organizationDbConnection, existingSalaryRecord, args, empJobDetails, basicPayRecord, salaryConfig, employeeContributionRate, employerContributionRate, gratuityAmount, loginEmployeeId, currentTimestamp, trx);
      } else {
        // Handle case where no record exists in old salary table - create new record
        await insertNewSalaryRecord(organizationDbConnection, args, empJobDetails, basicPayRecord, salaryConfig, employeeContributionRate, employerContributionRate, gratuityAmount, loginEmployeeId, currentTimestamp, trx);
      }
  
      // Handle allowances in configure_allowances table
      await handleConfigureAllowances(organizationDbConnection, args, loginEmployeeId, currentTimestamp, trx);
  
      return 'success';
    } catch (error) {
      console.log('Error in handleOldSalaryTableOperationsForRevision function', error);
      throw error;
    }
  }


  async function handleSalaryHistoryAndUpdate(organizationDbConnection, existingRecord, args, empJobDetails, basicPayRecord, salaryConfig, employeeContributionRate, employerContributionRate, gratuityAmount, loginEmployeeId, currentTimestamp, trx) {
    // Check if there are significant changes that require history
    const hasChanges = (
      existingRecord.Annual_Gross_Salary != args.annualGrossSalary ||
      existingRecord.Monthly_Gross_Salary != args.monthlyGrossSalary ||
      existingRecord.Basic_Pay != (basicPayRecord?.Amount || args.basicPay) ||
      moment(existingRecord.Effective_Date).format('YYYY-MM-DD') != moment(args.effectiveFrom).format('YYYY-MM-DD') ||
      existingRecord.Is_PfEmployee != (salaryConfig?.Eligible_For_Pf || 0) ||
      existingRecord.Is_ETFEmployee != (salaryConfig?.Eligible_For_Nps || 0) ||
      existingRecord.Is_InsuranceEmployee != (salaryConfig?.Eligible_For_Insurance || 0)
    );
  
    if (hasChanges) {
      const archiveDate = moment(args.effectiveFrom).subtract(1, 'day').format('YYYY-MM-DD');
  
      // Check if history record already exists
      const existingHistory = await organizationDbConnection('audit_salary')
        .select('*')
        .where('Employee_Id', args.employeeId)
        .where('Archive_Date', archiveDate)
        .first()
        .transacting(trx);
  
      const incrementDetails = getIncrementDetails(args, existingRecord);
  
      if (existingHistory) {
        // Update existing history record
        await organizationDbConnection('audit_salary')
          .update({
            Modified_On: currentTimestamp,
            Increment_Type: incrementDetails.Increment_Type,
            Increment_Amount: incrementDetails.Increment_Amount,
            Increment_Date: incrementDetails.Increment_Date
          })
          .where('Employee_Id', args.employeeId)
          .where('Archive_Date', archiveDate)
          .transacting(trx);
      } else {
        // Insert new history record only if effective dates are different
        if (moment(existingRecord.Effective_Date).format('YYYY-MM-DD') !== moment(args.effectiveFrom).format('YYYY-MM-DD')) {
          await organizationDbConnection('audit_salary')
            .insert({
              Employee_Id: args.employeeId,
              Location_Id: existingRecord.Location_Id,
              Grade_Id: existingRecord.Grade_Id,
              Represent_Basic_As_Multi_Components: existingRecord.Represent_Basic_As_Multi_Components,
              Basic_Component_One: existingRecord.Basic_Component_One,
              Basic_Component_Two: existingRecord.Basic_Component_Two,
              Basic_Pay: existingRecord.Basic_Pay,
              Effective_Date: existingRecord.Effective_Date,
              Annual_Gross_Salary: existingRecord.Annual_Gross_Salary,
              Monthly_Gross_Salary: existingRecord.Monthly_Gross_Salary,
              Annual_Ctc: existingRecord.Annual_Ctc,
              Archive_Date: archiveDate,
              Modified_By: loginEmployeeId,
              Is_OvertimeEmployee: existingRecord.Is_OvertimeEmployee,
              Overtime_Wage: existingRecord.Overtime_Wage,
              Modified_On: currentTimestamp,
              Is_PfEmployee: existingRecord.Is_PfEmployee,
              Is_ETFEmployee: existingRecord.Is_ETFEmployee,
              Employee_Contribution_Rate: existingRecord.Employee_Contribution_Rate,
              Employer_Contribution_Rate: existingRecord.Employer_Contribution_Rate,
              Is_InsuranceEmployee: existingRecord.Is_InsuranceEmployee,
              Flexible_Benefit_Plan_Type: existingRecord.Flexible_Benefit_Plan_Type,
              Flexible_Benefit_Plan_Amount: existingRecord.Flexible_Benefit_Plan_Amount,
              Flexible_Benefit_Plan_Percentage: existingRecord.Flexible_Benefit_Plan_Percentage,
              Increment_Type: incrementDetails.Increment_Type,
              Increment_Amount: incrementDetails.Increment_Amount,
              Increment_Date: incrementDetails.Increment_Date
            })
            .transacting(trx);
        }
      }
    }
  
    // Update the existing salary record
    await updateSalaryRecord(organizationDbConnection, args, empJobDetails, basicPayRecord, salaryConfig, employeeContributionRate, employerContributionRate, gratuityAmount, loginEmployeeId, currentTimestamp, trx);
  }
  
  /**
   * Insert new salary record
   */
  async function insertNewSalaryRecord(organizationDbConnection, args, empJobDetails, basicPayRecord, salaryConfig, employeeContributionRate, employerContributionRate, gratuityAmount, loginEmployeeId, currentTimestamp, trx) {
    const salaryData = {
      Employee_Id: args.employeeId,
      Location_Id: empJobDetails?.Location_Id || 0,
      Grade_Id: empJobDetails?.Grade_Id || 0,
      Represent_Basic_As_Multi_Components: 'No',
      Basic_Component_One: 0,
      Basic_Component_Two: 0,
      Basic_Pay: basicPayRecord?.Amount || args.basicPay,
      Effective_Date: args.effectiveFrom,
      ESI_Contribution_End_Date: null,
      Annual_Ctc: args.annualCTC,
      Annual_Gross_Salary: args.annualGrossSalary,
      Monthly_Gross_Salary: args.monthlyGrossSalary,
      Is_OvertimeEmployee: null,
      Overtime_Allocation:  0,
      Overtime_Wage_Index: 0,
      Overtime_Wage: 0,
      Is_PfEmployee: salaryConfig?.Eligible_For_Pf || 0,
      Exempt_EDLI: salaryConfig?.Exempt_EDLI || 0,
      Employee_Contribution_Rate: employeeContributionRate,
      Employer_Contribution_Rate: employerContributionRate,
      Is_InsuranceEmployee: salaryConfig?.Eligible_For_Insurance || 0,
      Is_ETFEmployee: salaryConfig?.Eligible_For_Nps || 0,
      Is_GratuityEmployee: salaryConfig?.Eligible_For_Gratuity || 0,
      Gratuity_Amount: gratuityAmount,
      Salary_Recalc: 0,
      Added_By: loginEmployeeId,
      Added_On: currentTimestamp,
      Lock_Flag: 0,
      Updated_By: loginEmployeeId,
      Updated_On: currentTimestamp,
      Reason_Id: 4,
      Flexible_Benefit_Plan_Type: null,
      Flexible_Benefit_Plan_Amount: null,
      Flexible_Benefit_Plan_Percentage: null
    };
  
    await organizationDbConnection('salary_details')
      .insert(salaryData)
      .transacting(trx);
  }
  
  /**
   * Update existing salary record
   */
  async function updateSalaryRecord(organizationDbConnection, args, empJobDetails, basicPayRecord, salaryConfig, employeeContributionRate, employerContributionRate, gratuityAmount, loginEmployeeId, currentTimestamp, trx) {
    const updateData = {
      Location_Id: empJobDetails?.Location_Id || 0,
      Grade_Id: empJobDetails?.Grade_Id || 0,
      Basic_Pay: basicPayRecord?.Amount || args.basicPay,
      Effective_Date: args.effectiveFrom,
      Annual_Ctc: args.annualCTC,
      Annual_Gross_Salary: args.annualGrossSalary,
      Monthly_Gross_Salary: args.monthlyGrossSalary,
      Is_PfEmployee: salaryConfig?.Eligible_For_Pf || 0,
      Exempt_EDLI: salaryConfig?.Exempt_EDLI || 0,
      Employee_Contribution_Rate: employeeContributionRate,
      Employer_Contribution_Rate: employerContributionRate,
      Is_InsuranceEmployee: salaryConfig?.Eligible_For_Insurance || 0,
      Is_ETFEmployee: salaryConfig?.Eligible_For_Nps || 0,
      Is_GratuityEmployee: salaryConfig?.Eligible_For_Gratuity || 0,
      Gratuity_Amount: gratuityAmount,
      Updated_By: loginEmployeeId,
      Updated_On: currentTimestamp
    };
    await organizationDbConnection('salary_details')
      .update(updateData)
      .where('Employee_Id', args.employeeId)
      .transacting(trx);
  
  }
  
  /**
   * Handle configure allowances table operations
   */
  async function handleConfigureAllowances(organizationDbConnection, args, loginEmployeeId, currentTimestamp, trx) {
    // Get existing allowances for the employee from allowances table
    const existingAllowances = await organizationDbConnection(ehrTables.allowances)
      .select('*')
      .where('Employee_Id', args.employeeId)
      .where('Coverage', 'EMP')
      .where('Allowance_Status', 'Active')
      .transacting(trx);
  
    // Make existing allowances inactive
    if (existingAllowances.length > 0) {
      const inactiveDate = moment(args.effectiveFrom).subtract(1, 'day').format('YYYY-MM-DD');
  
      await organizationDbConnection(ehrTables.allowances)
        .update({
          Allowance_Status: 'InActive',
          InActive_Date: inactiveDate,
          Updated_By: loginEmployeeId,
          Updated_On: currentTimestamp
        })
        .where('Employee_Id', args.employeeId)
        .where('Coverage', 'EMP')
        .where('Allowance_Status', 'Active')
        .transacting(trx);
    }
  
    // Insert new allowances from employee_salary_allowance
    if (args.allowance && args.allowance.length > 0) {
      const allowanceIds = args.allowance.map(a => a.allowanceId);
  
      const allowanceTypeDetails = await organizationDbConnection(ehrTables.allowances + ' as A')
        .select('A.Allowance_Id', 'A.Allowance_Type_Id', 'AT.Is_Flexi_Benefit_Plan')
        .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
        .whereIn('A.Allowance_Id', allowanceIds)
        .transacting(trx);
  
      const allowanceDataArray = args.allowance.map(allowanceItem => {
        const typeDetails = allowanceTypeDetails.find(detail => detail.Allowance_Id === allowanceItem.allowanceId);
  
        return {
          Allowance_Type_Id: typeDetails?.Allowance_Type_Id,
          Location_Id: null,
          Grade_Id: null,
          Coverage: 'EMP',
          Employee_Id: args.employeeId,
          Allowance_Type: allowanceItem.allowanceType,
          As_Is_Payment: 'No',
          Percentage: allowanceItem.allowanceType === 'Percentage' ? allowanceItem.percentage : 0,
          Amount: allowanceItem.allowanceType === 'Amount' ? allowanceItem.amount : 0,
          FBP_Max_Declaration: (allowanceItem.allowanceType === 'Amount' && typeDetails?.Is_Flexi_Benefit_Plan === 'Yes') ? allowanceItem.amount : null,
          Allowance_Status: 'Active',
          InActive_Date: null,
          Description: null,
          Added_On: currentTimestamp,
          Updated_On: currentTimestamp,
          Updated_By: loginEmployeeId,
          Lock_Flag: 0,
          Added_By: loginEmployeeId
        };
      });
  
      await organizationDbConnection(ehrTables.allowances)
        .insert(allowanceDataArray)
        .transacting(trx);
    }
  
    // Copy organization-level allowances that are not in employee_salary_allowance
    const employeeAllowanceIds = args.allowance ? args.allowance.map(a => a.allowanceId) : [];
  
    // Get organization allowances that are not assigned to this employee
    const orgAllowancesQuery = organizationDbConnection(ehrTables.allowances)
      .select('*')
      .where('Coverage', 'Org')
      .transacting(trx);
  
      if (employeeAllowanceIds.length > 0) {
      orgAllowancesQuery.whereNotIn('Allowance_Id', employeeAllowanceIds);
    }
  
    const orgAllowances = await orgAllowancesQuery;
  
    if (orgAllowances.length > 0) {
      const orgAllowanceDataArray = orgAllowances.map(orgAllowance => ({
        Allowance_Type_Id: orgAllowance.Allowance_Type_Id,
        Location_Id: null,
        Grade_Id: null,
        Coverage: 'EMP',
        Employee_Id: args.employeeId,
        Allowance_Type: 'Amount',
        As_Is_Payment: 'No',
        Percentage: 0,
        Amount: 0,
        FBP_Max_Declaration: null,
        Allowance_Status: 'Active',
        InActive_Date: null,
        Description: null,
        Added_On: currentTimestamp,
        Updated_On: currentTimestamp,
        Updated_By: loginEmployeeId,
        Lock_Flag: 0,
        Added_By: loginEmployeeId
      }));
  
      await organizationDbConnection(ehrTables.allowances)
        .insert(orgAllowanceDataArray)
        .transacting(trx);
    }
  }
  
  /**
   * Get increment details for history
   */
  function getIncrementDetails(newSalaryData, existingSalaryData, incrementType = '') {
    let incrementedSalary, existingSalary, finalIncrementType;
  
    if (!incrementType || incrementType === '' || incrementType === 'Gross_Annual_Salary') {
      incrementedSalary = parseFloat(newSalaryData.annualGrossSalary);
      existingSalary = parseFloat(existingSalaryData.Annual_Gross_Salary);
      finalIncrementType = 'Gross_Annual_Salary';
    } else {
      incrementedSalary = parseFloat(newSalaryData.monthlyGrossSalary);
      existingSalary = parseFloat(existingSalaryData.Monthly_Gross_Salary);
      finalIncrementType = 'Gross_Monthly_Salary';
    }
  
    const incrementAmount = incrementedSalary - existingSalary;
  
    if (incrementAmount > 0) {
      return {
        Increment_Amount: incrementAmount,
        Increment_Type: finalIncrementType,
        Increment_Date: newSalaryData.effectiveFrom
      };
    } else {
      return {
        Increment_Amount: 0,
        Increment_Type: 0,
        Increment_Date: newSalaryData.effectiveFrom
      };
    }
  }
  
const knex = require('knex');
const Common = require('../common');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require moment-timezone
const moment = require('moment-timezone');
const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');

/**
 * Function to get organization database connection based on organization code
 * @param {*} orgCode
 */
const getOrganizationDbConnection = async orgCode => {
   let connection = orgCode ? await commonLib.func.getDataBaseConnection(
    {
        stageName: process.env.stageName, dbPrefix: process.env.dbPrefix, dbSecretName: process.env.secretName,
        region: process.env.region, orgCode: orgCode, secretRequired: 1
    }) : null;

  return knex(connection.OrganizationDb); 
};

/**
 * Function to get app manager database connection
 * @param {*} orgCode
 */
const getAppManagerDbConnection = async orgCode => {

  let connection = orgCode ? await commonLib.func.getDataBaseConnection(
  {
      stageName: process.env.stageName, dbPrefix: process.env.dbPrefix, dbSecretName: process.env.secretName,
      region: process.env.region, orgCode: orgCode, secretRequired: 1
  }) : null;

  return knex(connection.AppManagerDb); 
};

/**
 * Funtion to get particular resingation details based on status filters
 * @param {*} args
 * @param {*} status
 */
const getEmployeeResignation = async (args, status = []) => {
  const { envelope, employeeId } = args;
  return new Promise(async resolve => {
    // return values
    var response = {
      error: null,
      result: null,
      relievingReasonDetails:null
    };
    try {
      // Get orgCode from envelope
      const organizationDbConnection = await getOrganizationDbConnection(
          envelope.orgCode
      );
      let  orgCode=envelope.orgCode;
      // prepare organization chart query
      const query = organizationDbConnection.select(
          'er.Resignation_Id as resignationId',
          'er.Employee_Id as employeeId',
          'ej.User_Defined_EmpId as employeeIdWithPrefix',
          'ej.Emp_Status as empStatus',
          organizationDbConnection.raw(
              'CONCAT(epi1.Emp_First_Name, \' \', epi1.Emp_Last_Name) as "employeeName"'
          ),
          'epi1.Photo_Path as employeePhotoPath',
          'epi1.Gender as employeeGender',
          'epi1.Is_Manager as employeeIsManager',
          'ej.Designation_Id as employeeDesignationId',
          'ds.Designation_Name as employeeDesignationName',
          'ej.Department_Id as employeeDepartmentId',
          'd.Department_Name as employeeDepartmentName',
          'er.Approver_Id as approverId',
          organizationDbConnection.raw(
              'CONCAT(epi2.Emp_First_Name, \' \', epi2.Emp_Last_Name) as "approverName"'
          ),
          'er.Approval_Status as resignationStatus',
          'er.Notice_Date as appliedDate',
          'er.Resignation_Date as resignationDate',
          'er.Workflow_Instance_Id as workflowInstanceId',
          'er.Workflow_Status as workflowStatus',
          'er.Added_On as addedOn',
          'er.Added_By as addedUserId',
          organizationDbConnection.raw(
              'CONCAT(epi3.Emp_First_Name, \' \', epi3.Emp_Last_Name) as "addedUserName"'
          ),
          'er.Updated_On as updatedOn',
          'er.Updated_By as updatedUserId',
          organizationDbConnection.raw(
              'CONCAT(epi4.Emp_First_Name, \' \', epi4.Emp_Last_Name) as "updatedUserName"'
          ),
          'er.Reason_Id as reasonId',
          'esr.ESIC_Reason as esicReasonName',
          'er.Withdrawn_Cancellation_Comment as withdrawnCancellationComment',
          'er.Relieving_Reason_Comment as relievingReasonComment'
      );
      query.from('emp_resignation AS er');
      query.leftJoin(
          'emp_personal_info as epi1',
          'er.Employee_Id',
          'epi1.Employee_Id'
      );
      query.leftJoin('emp_job as ej', 'er.Employee_Id', 'ej.Employee_Id');
      query.leftJoin('department as d', 'ej.Department_Id', 'd.Department_Id');
      query.leftJoin(
          'designation as ds',
          'ej.Designation_Id',
          'ds.Designation_Id'
      );
      query.leftJoin(
          'emp_personal_info as epi2',
          'er.Approver_Id',
          'epi2.Employee_Id'
      );
      query.leftJoin(
          'emp_personal_info as epi3',
          'er.Added_By',
          'epi3.Employee_Id'
      );
      query.leftJoin(
          'emp_personal_info as epi4',
          'er.Updated_By',
          'epi4.Employee_Id'
      );
      query.leftJoin(
        'esic_reason as esr',
        'er.Reason_Id',
        'esr.Reason_Id'
      );
      // check resignationId exist in args. If yes, get the resignation record by using resignationId
      if (args.resignationId) {
        query.where('er.Resignation_Id', '=', args.resignationId);
      }
      // check resignationId exist in args. If not, get the resignation record by using employeeId
      if (employeeId && !args.resignationId){
        query.where('er.Employee_Id', '=', employeeId);
      }
      if (status && status.length > 0) {
        query.whereIn('er.Approval_Status', status)
      }

      return query
          .then(async function(result) {
            response.result =
                result.length > 0
                    ? {
                      ...result[0],
                      employeePhotoPath: result[0].employeePhotoPath
                          ? await Common.getFileURL(
                              envelope.orgCode,
                              Common.constant.awsImageBucket,
                              result[0].employeePhotoPath
                          )
                          : null,
                      appliedDate: 
                        await formDate(new Date(result[0].appliedDate).toDateString()), // call function formDate to changes applied date from UTC time to local time
                      resignationDate: 
                        await formDate(new Date(result[0].resignationDate).toDateString()), // call function formDate to changes resignation date from UTC time to local time
                      addedOn: Common.getFormattedDateString(
                          null,
                          Common.constant.YYYYMMDDHHmmss,
                          result[0].addedOn
                      ),
                      updatedOn: result[0].updatedOn
                          ? Common.getFormattedDateString(
                              null,
                              Common.constant.YYYYMMDDHHmmss,
                              result[0].updatedOn
                          )
                          : null
                    }
                    : null;
            
            if(response.result && response.result.resignationDate &&  response.result.employeeId)
            {
                let exitDate=moment(response.result.resignationDate);
                let employeeId=response.result.employeeId;
                let exitMonth=exitDate.format('MM');
                let exitYear=exitDate.format('YYYY');
                exitDate=exitDate.format('YYYY-MM-DD');
                let salaryDate=await commonLib.func.getSalaryDay(orgCode,organizationDbConnection,exitMonth,exitYear,exitDate);
                let lastSalaryDate=salaryDate.Last_SalaryDate;
                lastSalaryDate=moment(lastSalaryDate).format('M,YYYY');
                let checkSalaryPayslip=await getSalaryPayslip(organizationDbConnection,lastSalaryDate,employeeId);
                if(checkSalaryPayslip.length>0)
                {
                  response.result['isPayslipGenerated']=1;
                }
                else{
                  response.result['isPayslipGenerated']=0;
                }
            }
            /** Get employee relieving reasons */
            let employeeRelievingReason = await Common.getEmployeeRelievingReaason(organizationDbConnection);

            /** Return the relieving reason details */
            response.relievingReasonDetails = employeeRelievingReason;

            resolve(response)
          })
          .catch(function(error) {
            console.log('Error while retrieving the resignation details', error);
            response.error = Common.getError(error, 'ERE0004');
            resolve(response)
          })
          .finally(function() {
            organizationDbConnection.destroy();
          })
    } catch (error) {
      console.log('Error while getting the resignation details', error);
      response.error = Common.getError(error, 'ERE0104');
      resolve(response)
    }
  })
};

/**
 * Function to get dynamic form template details
 * @param {*} organizationDbConnection
 * @param {*} templateId
 */
const getDynamicFormTemplate = async (orgCode, templateId) => {
  // Get orgCode from envelope
  const organizationDbConnection = await getOrganizationDbConnection(orgCode);

  // to get a particular dynamic form template details
  return new Promise(resolve => {
    const query = organizationDbConnection.select(
        'dfb.Template_Id as templateId',
        'dfb.Template_Name as templateName',
        'dfb.Form_Template as template',
        'dfb.Conversational as conversational'
    );
    query.from('dynamic_form_builder AS dfb');
    query.where('dfb.Template_Id', '=', templateId);
    query
        .then(function(result) {
          resolve({
            result: result.length > 0 ? result[0] : null
          })
        })
        .catch(error => {
          resolve({
            error
          })
        })
        .finally(() => {
          organizationDbConnection.destroy();
        })
  })
};

/**
 * Function to get dynamic form details
 * @param {*} organizationDbConnection
 * @param {*} taskId
 */
const getDynamicFormResponse = async (orgCode, taskId) => {
  // Get orgCode from envelope
  const organizationDbConnection = await getOrganizationDbConnection(orgCode);

  // to get a particular dynamic form details
  return new Promise(resolve => {

    const query = organizationDbConnection.select(
        'dfr.Response_Id as formResponseId',
        'dfr.Response as formResponse',
        'dfr.Status as formStatus',
        'dfr.Added_By as addedUserId',
        organizationDbConnection.raw(
            'CONCAT(epi1.Emp_First_Name, \' \', epi1.Emp_Last_Name) as "addedUserName"'
        ),
        'dfr.Added_On as addedOn',
        'dfr.Updated_By as updatedUserId',
        organizationDbConnection.raw(
            'CONCAT(epi2.Emp_First_Name, \' \', epi2.Emp_Last_Name) as "updatedUserName"'
        ),
        'dfr.Updated_On as updatedOn'
    );
    query.from('dynamic_form_responses AS dfr');
    query.leftJoin(
        'emp_personal_info as epi1',
        'dfr.Added_By',
        'epi1.Employee_Id'
    );
    query.leftJoin(
        'emp_personal_info as epi2',
        'dfr.Updated_By',
        'epi2.Employee_Id'
    );
    query.where('dfr.Task_Id', '=', taskId ? taskId : '');

    query
        .then(function(result) {
          resolve({
            result:
                result.length > 0
                    ? {
                      ...result[0],
                      addedOn: Common.getFormattedDateString(
                          null,
                          Common.constant.YYYYMMDDHHmmss,
                          result[0].addedOn
                      ),
                      updatedOn: result[0].updatedOn
                          ? Common.getFormattedDateString(
                              null,
                              Common.constant.YYYYMMDDHHmmss,
                              result[0].updatedOn
                          )
                          : null
                    }
                    : null
          })
        })
        .catch(error => {
          resolve({
            error
          })
        })
        .finally(() => {
          organizationDbConnection.destroy();
        })
  })
};

/**
 * For get resignation form details with workflow instance id
 */

async function getResignationDynamicFormDetail(
    orgCode,
    workflowInstanceId,
    employeeId
) {
  return new Promise(async resolve => {

    const organizationDbConnection = await getOrganizationDbConnection(orgCode);
    const query = organizationDbConnection.select(
        'task_id as taskId',
        'assignee as assignee',
        'form_identifier as formIdentifier'
    );
    query.from('ta_user_task');
    query.where('process_instance_id', workflowInstanceId);
    query.andWhere('assignee', employeeId);
    query
        .then(async result => {
          var returnValues = {};
          let dynamicFormId = null;
          // check form mapped to this task and then get formId
          if (result && result.length > 0) {
            const row = result[0];
            returnValues['workflowTaskId'] = row.taskId;
            dynamicFormId = row.formIdentifier
          }
          // if form available means return task detail with form details.
          if (dynamicFormId) {
            const {
              error: dynamicFormTemplateError,
              result: dynamicFormTemplateResult
            } = await getDynamicFormTemplate(orgCode, dynamicFormId);
            if (dynamicFormTemplateError) {
              console.log(
                  'Error while get dynamic form details, ',
                  dynamicFormTemplateError
              )
            } else {
              returnValues['dynamicFormTemplate'] = dynamicFormTemplateResult
            }
          }
          resolve({ result: returnValues })
        })
        .catch(error => {
          resolve({ error })
        })
        .finally(() => {
          organizationDbConnection.destroy();
        })
  })
}

async function getAllSubEmployee(organizationDbConnection, employeeId) {
  return new Promise(resolve => {
    const query = organizationDbConnection.select('Employee_Id');
    query.from('emp_job');
    query.where('Manager_Id', employeeId);
    query.then(result => {
      resolve(
          result.map(row => {
            return row.Employee_Id
          })
      )
    })
  })
}

async function getServiceProviderId(organizationDbConnection, employeeId) {
  // Get the service provider id associated with the employee
  try{
    return(
      organizationDbConnection('emp_job')
      .select('Service_Provider_Id')
      .where('Employee_Id', employeeId)
      .then(serviceProviderId =>{
          return serviceProviderId.length>0 ? serviceProviderId[0].Service_Provider_Id :'';
      })
      .catch(error => {
          console.log('Error in getServiceProviderId function .catch block.',error);
          return '';
      })
    );
  } catch(getServiceProviderIdError){
    console.log('Error in getServiceProviderId function .catch block.',getServiceProviderIdError);
    return '';
  }
}

async function getAllServiceProviderEmployees(organizationDbConnection, serviceProviderId) {
  //  to get the employees associated with the service provider
  try{
    return(
      organizationDbConnection('emp_job')
      .select('Employee_Id')
      .where('Service_Provider_Id', serviceProviderId)
      .then(getEmployees =>{
          let employeeIds = [];
          getEmployees.length > 0 ? getEmployees.map(row => {
            employeeIds.push(row.Employee_Id)
          }) : [];

          return employeeIds;
      })
      .catch(error => {
          console.log('Error in getAllServiceProviderEmployees function .catch block.',error);
          return [];
      })
    );
  } catch(getAllServiceProviderEmployeesError){
    console.log('Error in getAllServiceProviderEmployees function .catch block.',getAllServiceProviderEmployeesError);
    return [];
  }

}


async function getEmployeeNotificationPeriodDays(organizationDbConnection, employeeId) {
  // for get employee designation and notice period days
  return new Promise(resolve => {
    const query = organizationDbConnection.select(
        'e.Designation_Id as designationId',
        'e.Confirmed as isConfirmed',
        'd.Notice_Period_Days_Within_Probation as noticePeriodDaysWithinProbation',
        'd.Notice_Period_Days_After_Probation as noticePeriodDaysAfterProbation'
    );
    query.from('emp_job as e');
    query.leftJoin( 'designation as d', 'e.Designation_Id', 'd.Designation_Id' );
    query.where('e.Employee_Id', employeeId);
    query
        .then(result => {

          if(result[0])
          {
            result = result[0];
            result.noticePeriodDays = result.isConfirmed ? result.noticePeriodDaysAfterProbation : result.noticePeriodDaysWithinProbation;

            resolve({
              result
            })
          }
          else
          {
            resolve({
              error : "ERE0012"
            })
          }
        })
        .catch(error => {
          resolve({
            error
          })
        })
  })
}

// function to form date from UCT time to local time (YYYY-MM-DD)
async function formDate(date) {
  try{
    // variable initialization
    var months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    // split the input by space
    var tmpDate = date.split(" ");
    // Get the index of the month and add 1 to get the month number
    var monthIndex = (months.findIndex(months => months == tmpDate[1]) + 1);
    // check if monthIndexis lessthan or equlato 9 if yes append 0 before the index
    if (monthIndex <= 9) {
      monthIndex = "0" + monthIndex;
    }
    // return response
    return tmpDate[3] + '-' + monthIndex + '-' + tmpDate[2];
  }catch(error){
    console.log("Error while forming the applied and exit date", error);
    // return response
    return "";
  }
  

}

async function getSalaryPayslip(organizationDbConnection,salaryMonth,employeeId)
{
  try{
    return(
      organizationDbConnection(ehrTables.salaryPayslip)
      .pluck('Payslip_Id')
      .where('Salary_Month',salaryMonth)
      .where('Employee_Id',employeeId)
      .then(data=>{
        return data;
      })
      .catch(e=>{
        console.log('Error in getSalaryPayslip .catch block',e);
        throw('PBP0101')
      })
    )
  }
  catch(e){
    console.log('Error in getSalaryPayslip main catch block.',e);
    throw('PBP0101')
  }
}

module.exports = {
  getOrganizationDbConnection,
  getEmployeeResignation,
  getDynamicFormTemplate,
  getDynamicFormResponse,
  getResignationDynamicFormDetail,
  getAllSubEmployee,
  getServiceProviderId,
  getAllServiceProviderEmployees,
  getEmployeeNotificationPeriodDays,
  formDate,
  getAppManagerDbConnection
};
